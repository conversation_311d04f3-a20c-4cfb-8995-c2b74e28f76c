<template>
  <div class="user-security-settings space-y-6">
    <!-- Authentication Methods -->
    <div class="card bg-base-100 shadow-xl border border-base-200">
      <div class="card-body">
        <h3 class="card-title text-primary mb-6">
          <Icon name="key" size="md" />
          Authentication Methods
        </h3>

        <!-- PIN Authentication -->
        <div class="border border-base-300 rounded-lg p-4 mb-4">
          <div class="flex items-center justify-between mb-3">
            <div class="flex items-center space-x-3">
              <Icon name="unlock" size="md" class="text-secondary" />
              <div>
                <h4 class="font-semibold">PIN Authentication</h4>
                <p class="text-sm text-base-content/70">6-digit PIN for quick access</p>
              </div>
            </div>
            <div class="badge" :class="pinStatus.badgeClass">
              {{ pinStatus.text }}
            </div>
          </div>



          <!-- PIN Setup/Management -->
          <div v-if="!hasPinSetup" class="space-y-3">
            <p class="text-sm text-base-content/60">Set up a PIN for faster login access</p>
            <div class="flex space-x-2">
              <button
                @click="openPinSetup"
                class="btn btn-primary btn-sm"
                :disabled="pinLoading"
              >
                <Icon name="plus" size="sm" class="mr-1" />
                Set Up PIN
              </button>
            </div>
          </div>

          <div v-else class="space-y-3">
            <p class="text-sm text-success">
              <Icon name="check-circle" size="sm" class="inline mr-1" />
              PIN authentication is active
            </p>
            <div class="flex space-x-2">
              <button
                @click="openPinChange"
                class="btn btn-outline btn-sm"
                :disabled="pinLoading"
              >
                <Icon name="edit" size="sm" class="mr-1" />
                Change PIN
              </button>
              <button
                @click="openPinRemove"
                class="btn btn-error btn-outline btn-sm"
                :disabled="pinLoading"
              >
                <Icon name="trash" size="sm" class="mr-1" />
                Remove PIN
              </button>
            </div>
          </div>
        </div>

        <!-- Biometric Authentication -->
        <div class="border border-base-300 rounded-lg p-4">
          <div class="flex items-center justify-between mb-3">
            <div class="flex items-center space-x-3">
              <Icon name="fingerprint" size="md" class="text-accent" />
              <div>
                <h4 class="font-semibold">Biometric Authentication</h4>
                <p class="text-sm text-base-content/70">Fingerprint or Face ID</p>
              </div>
            </div>
            <div class="badge" :class="biometricStatus.badgeClass">
              {{ biometricStatus.text }}
            </div>
          </div>

          <!-- Biometric Error Message -->
          <div v-if="biometricError" class="alert alert-error mb-4">
            <Icon name="exclamation-triangle" size="sm" />
            <span class="text-sm">{{ biometricError }}</span>
          </div>

          <!-- Biometric Setup/Management -->
          <div v-if="!biometricAvailable" class="space-y-3">
            <p class="text-sm text-warning">
              <Icon name="exclamation-triangle" size="sm" class="inline mr-1" />
              Biometric authentication is not available on this device
            </p>
          </div>

          <div v-else-if="!hasBiometricSetup" class="space-y-3">
            <p class="text-sm text-base-content/60">Enable biometric authentication for secure access</p>
            <div class="flex space-x-2">
              <button 
                @click="setupBiometric" 
                class="btn btn-primary btn-sm"
                :disabled="biometricLoading"
                :class="{ 'loading': biometricLoading }"
              >
                <Icon v-if="!biometricLoading" name="fingerprint" size="sm" class="mr-1" />
                {{ biometricLoading ? 'Setting up...' : 'Enable Biometric' }}
              </button>
            </div>
          </div>

          <div v-else class="space-y-3">
            <p class="text-sm text-success">
              <Icon name="check-circle" size="sm" class="inline mr-1" />
              Biometric authentication is active
            </p>
            <div class="flex space-x-2">
              <button 
                @click="testBiometric" 
                class="btn btn-outline btn-sm"
                :disabled="biometricLoading"
              >
                <Icon name="play" size="sm" class="mr-1" />
                Test Biometric
              </button>
              <button 
                @click="removeBiometricAuth" 
                class="btn btn-error btn-outline btn-sm"
                :disabled="biometricLoading"
              >
                <Icon name="trash" size="sm" class="mr-1" />
                Remove Biometric
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Security Preferences -->
    <div class="card bg-base-100 shadow-xl border border-base-200">
      <div class="card-body">
        <h3 class="card-title text-primary mb-6">
          <Icon name="shield" size="md" />
          Security Preferences
        </h3>

        <div class="space-y-4">
          <label class="label cursor-pointer justify-start">
            <input
              type="checkbox"
              v-model="preferences.rememberDevice"
              class="checkbox checkbox-primary"
            />
            <span class="label-text ml-3">Remember this device for 30 days</span>
          </label>

          <label class="label cursor-pointer justify-start">
            <input
              type="checkbox"
              v-model="preferences.requireBiometricForSensitive"
              class="checkbox checkbox-primary"
              :disabled="!hasBiometricSetup"
            />
            <span class="label-text ml-3">Require biometric for sensitive actions</span>
          </label>

          <label class="label cursor-pointer justify-start">
            <input
              type="checkbox"
              v-model="preferences.autoLogoutOnInactivity"
              class="checkbox checkbox-primary"
            />
            <span class="label-text ml-3">Auto-logout after 30 minutes of inactivity</span>
          </label>
        </div>

        <div class="card-actions justify-end mt-6">
          <button 
            @click="savePreferences" 
            class="btn btn-primary"
            :disabled="preferencesLoading"
            :class="{ 'loading': preferencesLoading }"
          >
            {{ preferencesLoading ? 'Saving...' : 'Save Preferences' }}
          </button>
        </div>
      </div>
    </div>

    <!-- PIN Setup Modal -->
    <div v-if="showPinSetup" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-base-100 rounded-lg shadow-xl max-w-md w-full mx-4 p-6">
        <h3 class="text-xl font-bold mb-4">Set Up PIN Authentication</h3>
        
        <div class="space-y-4">
          <p class="text-sm text-base-content/70">Choose a 6-digit PIN for quick access</p>

          <!-- PIN Error Message -->
          <div v-if="pinError" class="alert alert-error">
            <Icon name="exclamation-triangle" size="sm" />
            <span class="text-sm">{{ pinError }}</span>
          </div>

          <!-- PIN Input -->
          <div class="flex justify-center space-x-2 mb-4">
            <input
              v-for="(digit, index) in newPinDigits"
              :key="index"
              :ref="(el) => setPinInputRef(el as HTMLInputElement, index)"
              :data-pin-index="index"
              type="password"
              maxlength="1"
              v-model="newPinDigits[index]"
              @input="handlePinInput(index, $event)"
              @keydown="handlePinKeydown(index, $event)"
              class="input input-bordered w-12 h-12 text-center text-lg font-bold"
              :class="{ 'input-error': pinError }"
              :disabled="pinLoading"
            />
          </div>

          <!-- PIN Requirements -->
          <div class="text-xs text-base-content/60 mb-4">
            <p>• Use 6 digits only</p>
            <p>• Avoid simple patterns (123456, 111111)</p>
            <p>• Use backspace to delete digits</p>
          </div>

          <div class="flex space-x-3">
            <button 
              @click="showPinSetup = false" 
              class="btn btn-ghost flex-1"
              :disabled="pinLoading"
            >
              Cancel
            </button>
            <button
              @click="createPin"
              class="btn btn-primary flex-1"
              :disabled="pinLoading || newPinDigits.some(d => !d)"
            >
              <span v-if="pinLoading" class="loading loading-spinner loading-sm"></span>
              {{ pinLoading ? 'Creating...' : 'Create PIN' }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- PIN Change Modal -->
    <div v-if="showPinChange" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-base-100 rounded-lg shadow-xl max-w-md w-full mx-4 p-6">
        <h3 class="text-xl font-bold mb-4">Change PIN</h3>

        <div class="space-y-6">
          <!-- PIN Error Message -->
          <div v-if="pinError" class="alert alert-error">
            <Icon name="exclamation-triangle" size="sm" />
            <span class="text-sm">{{ pinError }}</span>
          </div>

          <!-- Current PIN -->
          <div>
            <label class="text-sm font-medium text-base-content/70 mb-2 block">Current PIN</label>
            <div class="flex justify-center space-x-2">
              <input
                v-for="(digit, index) in currentPinDigits"
                :key="`current-${index}`"
                :ref="(el) => setCurrentPinInputRef(el as HTMLInputElement, index)"
                :data-current-pin-index="index"
                type="password"
                maxlength="1"
                v-model="currentPinDigits[index]"
                @input="handleCurrentPinInput(index, $event)"
                @keydown="handleCurrentPinKeydown(index, $event)"
                class="input input-bordered w-12 h-12 text-center text-lg font-bold"
                :class="{ 'input-error': pinError }"
                :disabled="pinLoading"
              />
            </div>
          </div>

          <!-- New PIN -->
          <div>
            <label class="text-sm font-medium text-base-content/70 mb-2 block">New PIN</label>
            <div class="flex justify-center space-x-2">
              <input
                v-for="(digit, index) in newPinDigits"
                :key="`new-${index}`"
                :ref="(el) => setPinInputRef(el as HTMLInputElement, index)"
                :data-pin-index="index"
                type="password"
                maxlength="1"
                v-model="newPinDigits[index]"
                @input="handlePinInput(index, $event)"
                @keydown="handlePinKeydown(index, $event)"
                class="input input-bordered w-12 h-12 text-center text-lg font-bold"
                :class="{ 'input-error': pinError }"
                :disabled="pinLoading"
              />
            </div>
          </div>

          <!-- Confirm New PIN -->
          <div>
            <label class="text-sm font-medium text-base-content/70 mb-2 block">Confirm New PIN</label>
            <div class="flex justify-center space-x-2">
              <input
                v-for="(digit, index) in confirmPinDigits"
                :key="`confirm-${index}`"
                :ref="(el) => setConfirmPinInputRef(el as HTMLInputElement, index)"
                :data-confirm-pin-index="index"
                type="password"
                maxlength="1"
                v-model="confirmPinDigits[index]"
                @input="handleConfirmPinInput(index, $event)"
                @keydown="handleConfirmPinKeydown(index, $event)"
                class="input input-bordered w-12 h-12 text-center text-lg font-bold"
                :class="{ 'input-error': pinError }"
                :disabled="pinLoading"
              />
            </div>
          </div>

          <!-- PIN Requirements -->
          <div class="text-xs text-base-content/60">
            <p>• Use 6 digits only</p>
            <p>• Avoid simple patterns (123456, 111111)</p>
            <p>• New PIN must be different from current PIN</p>
          </div>

          <div class="flex space-x-3">
            <button
              @click="showPinChange = false"
              class="btn btn-ghost flex-1"
              :disabled="pinLoading"
            >
              Cancel
            </button>
            <button
              @click="changePin"
              class="btn btn-primary flex-1"
              :disabled="pinLoading || currentPinDigits.some(d => !d) || newPinDigits.some(d => !d) || confirmPinDigits.some(d => !d)"
            >
              <span v-if="pinLoading" class="loading loading-spinner loading-sm"></span>
              {{ pinLoading ? 'Updating...' : 'Update PIN' }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- PIN Remove Modal -->
    <div v-if="showPinRemove" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-base-100 rounded-lg shadow-xl max-w-md w-full mx-4 p-6">
        <h3 class="text-xl font-bold mb-4">Remove PIN</h3>

        <div class="space-y-4">
          <div class="alert alert-warning">
            <Icon name="exclamation-triangle" size="sm" />
            <span class="text-sm">This will permanently remove your PIN authentication. You can set up a new PIN later.</span>
          </div>

          <!-- PIN Error Message -->
          <div v-if="pinError" class="alert alert-error">
            <Icon name="exclamation-triangle" size="sm" />
            <span class="text-sm">{{ pinError }}</span>
          </div>

          <!-- Current PIN for confirmation -->
          <div>
            <label class="text-sm font-medium text-base-content/70 mb-2 block">Enter your current PIN to confirm removal</label>
            <div class="flex justify-center space-x-2">
              <input
                v-for="(digit, index) in currentPinDigits"
                :key="`remove-${index}`"
                :ref="(el) => setCurrentPinInputRef(el as HTMLInputElement, index)"
                :data-current-pin-index="index"
                type="password"
                maxlength="1"
                v-model="currentPinDigits[index]"
                @input="handleCurrentPinInput(index, $event)"
                @keydown="handleCurrentPinKeydown(index, $event)"
                class="input input-bordered w-12 h-12 text-center text-lg font-bold"
                :class="{ 'input-error': pinError }"
                :disabled="pinLoading"
              />
            </div>
          </div>

          <div class="flex space-x-3">
            <button
              @click="showPinRemove = false"
              class="btn btn-ghost flex-1"
              :disabled="pinLoading"
            >
              Cancel
            </button>
            <button
              @click="removePin"
              class="btn btn-error flex-1"
              :disabled="pinLoading || currentPinDigits.some(d => !d)"
            >
              <span v-if="pinLoading" class="loading loading-spinner loading-sm"></span>
              {{ pinLoading ? 'Removing...' : 'Remove PIN' }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { authService } from '@/services/auth'
import { biometricService } from '@/services/biometric'
import Icon from '@/components/common/Icon.vue'

// Store
const authStore = useAuthStore()

// State
const hasPinSetup = ref(false)
const hasBiometricSetup = ref(false)
const biometricAvailable = ref(false)

// Loading states
const pinLoading = ref(false)
const biometricLoading = ref(false)
const preferencesLoading = ref(false)

// Error states
const pinError = ref('')
const biometricError = ref('')

// Modal states
const showPinSetup = ref(false)
const showPinChange = ref(false)
const showPinRemove = ref(false)

// PIN input
const newPinDigits = ref(['', '', '', '', '', ''])
const currentPinDigits = ref(['', '', '', '', '', ''])
const confirmPinDigits = ref(['', '', '', '', '', ''])

// PIN input refs - using a single ref array for dynamic refs
const pinInputRefs = ref<HTMLInputElement[]>([])
const currentPinInputRefs = ref<HTMLInputElement[]>([])
const confirmPinInputRefs = ref<HTMLInputElement[]>([])

// Preferences
const preferences = reactive({
  rememberDevice: true,
  requireBiometricForSensitive: false,
  autoLogoutOnInactivity: true
})

// Computed
const pinStatus = computed(() => {
  if (hasPinSetup.value) {
    return { text: 'Active', badgeClass: 'badge-success' }
  }
  return { text: 'Not Set', badgeClass: 'badge-warning' }
})

const biometricStatus = computed(() => {
  if (!biometricAvailable.value) {
    return { text: 'Not Available', badgeClass: 'badge-error' }
  }
  if (hasBiometricSetup.value) {
    return { text: 'Active', badgeClass: 'badge-success' }
  }
  return { text: 'Not Set', badgeClass: 'badge-warning' }
})

// Methods
const clearErrors = () => {
  pinError.value = ''
  biometricError.value = ''
}

const clearPinInputs = () => {
  newPinDigits.value = ['', '', '', '', '', '']
  currentPinDigits.value = ['', '', '', '', '', '']
  confirmPinDigits.value = ['', '', '', '', '', '']
}

const setPinInputRef = (el: HTMLInputElement | null, index: number) => {
  if (el) {
    pinInputRefs.value[index] = el
  }
}

const setCurrentPinInputRef = (el: HTMLInputElement | null, index: number) => {
  if (el) {
    currentPinInputRefs.value[index] = el
  }
}

const setConfirmPinInputRef = (el: HTMLInputElement | null, index: number) => {
  if (el) {
    confirmPinInputRefs.value[index] = el
  }
}

const openPinSetup = () => {
  clearErrors()
  clearPinInputs()
  showPinSetup.value = true
  // Focus first input after modal opens
  nextTick(() => {
    const firstInput = getPinInput(0)
    firstInput?.focus()
  })
}

const openPinChange = () => {
  clearErrors()
  clearPinInputs()
  showPinChange.value = true
  // Focus first current PIN input after modal opens
  nextTick(() => {
    const firstInput = getCurrentPinInput(0)
    firstInput?.focus()
  })
}

const openPinRemove = () => {
  clearErrors()
  clearPinInputs()
  showPinRemove.value = true
  // Focus first current PIN input after modal opens
  nextTick(() => {
    const firstInput = getCurrentPinInput(0)
    firstInput?.focus()
  })
}

const handlePinInput = (index: number, event: Event) => {
  const target = event.target as HTMLInputElement
  const value = target.value

  // Clear errors when user starts typing
  if (pinError.value) {
    pinError.value = ''
  }

  if (value && /^\d$/.test(value)) {
    newPinDigits.value[index] = value

    // Move to next input
    if (index < 5) {
      nextTick(() => {
        const nextInput = getPinInput(index + 1)
        nextInput?.focus()
      })
    }
  } else {
    target.value = ''
    newPinDigits.value[index] = ''
  }
}

const handleCurrentPinInput = (index: number, event: Event) => {
  const target = event.target as HTMLInputElement
  const value = target.value

  // Clear errors when user starts typing
  if (pinError.value) {
    pinError.value = ''
  }

  if (value && /^\d$/.test(value)) {
    currentPinDigits.value[index] = value

    // Move to next input
    if (index < 5) {
      nextTick(() => {
        const nextInput = getCurrentPinInput(index + 1)
        nextInput?.focus()
      })
    }
  } else {
    target.value = ''
    currentPinDigits.value[index] = ''
  }
}

const handleConfirmPinInput = (index: number, event: Event) => {
  const target = event.target as HTMLInputElement
  const value = target.value

  // Clear errors when user starts typing
  if (pinError.value) {
    pinError.value = ''
  }

  if (value && /^\d$/.test(value)) {
    confirmPinDigits.value[index] = value

    // Move to next input
    if (index < 5) {
      nextTick(() => {
        const nextInput = getConfirmPinInput(index + 1)
        nextInput?.focus()
      })
    }
  } else {
    target.value = ''
    confirmPinDigits.value[index] = ''
  }
}

const handlePinKeydown = (index: number, event: KeyboardEvent) => {
  // Handle backspace to move to previous input and clear current
  if (event.key === 'Backspace') {
    if (newPinDigits.value[index]) {
      // Clear current digit
      newPinDigits.value[index] = ''
    } else if (index > 0) {
      // Move to previous input and clear it
      newPinDigits.value[index - 1] = ''
      nextTick(() => {
        const prevInput = getPinInput(index - 1)
        prevInput?.focus()
      })
    }
  }
  // Handle delete key
  else if (event.key === 'Delete') {
    newPinDigits.value[index] = ''
  }
  // Handle arrow keys for navigation
  else if (event.key === 'ArrowLeft' && index > 0) {
    nextTick(() => {
      const prevInput = getPinInput(index - 1)
      prevInput?.focus()
    })
  }
  else if (event.key === 'ArrowRight' && index < 5) {
    nextTick(() => {
      const nextInput = getPinInput(index + 1)
      nextInput?.focus()
    })
  }
}

const handleCurrentPinKeydown = (index: number, event: KeyboardEvent) => {
  // Handle backspace to move to previous input and clear current
  if (event.key === 'Backspace') {
    if (currentPinDigits.value[index]) {
      // Clear current digit
      currentPinDigits.value[index] = ''
    } else if (index > 0) {
      // Move to previous input and clear it
      currentPinDigits.value[index - 1] = ''
      nextTick(() => {
        const prevInput = getCurrentPinInput(index - 1)
        prevInput?.focus()
      })
    }
  }
  // Handle delete key
  else if (event.key === 'Delete') {
    currentPinDigits.value[index] = ''
  }
  // Handle arrow keys for navigation
  else if (event.key === 'ArrowLeft' && index > 0) {
    nextTick(() => {
      const prevInput = getCurrentPinInput(index - 1)
      prevInput?.focus()
    })
  }
  else if (event.key === 'ArrowRight' && index < 5) {
    nextTick(() => {
      const nextInput = getCurrentPinInput(index + 1)
      nextInput?.focus()
    })
  }
}

const handleConfirmPinKeydown = (index: number, event: KeyboardEvent) => {
  // Handle backspace to move to previous input and clear current
  if (event.key === 'Backspace') {
    if (confirmPinDigits.value[index]) {
      // Clear current digit
      confirmPinDigits.value[index] = ''
    } else if (index > 0) {
      // Move to previous input and clear it
      confirmPinDigits.value[index - 1] = ''
      nextTick(() => {
        const prevInput = getConfirmPinInput(index - 1)
        prevInput?.focus()
      })
    }
  }
  // Handle delete key
  else if (event.key === 'Delete') {
    confirmPinDigits.value[index] = ''
  }
  // Handle arrow keys for navigation
  else if (event.key === 'ArrowLeft' && index > 0) {
    nextTick(() => {
      const prevInput = getConfirmPinInput(index - 1)
      prevInput?.focus()
    })
  }
  else if (event.key === 'ArrowRight' && index < 5) {
    nextTick(() => {
      const nextInput = getConfirmPinInput(index + 1)
      nextInput?.focus()
    })
  }
}

const getPinInput = (index: number): HTMLInputElement | null => {
  // Get input by template ref array
  const refElement = pinInputRefs.value[index]
  if (refElement) return refElement

  // Fallback to querySelector
  return document.querySelector(`input[data-pin-index="${index}"]`) as HTMLInputElement
}

const getCurrentPinInput = (index: number): HTMLInputElement | null => {
  // Get current PIN input by template ref array
  const refElement = currentPinInputRefs.value[index]
  if (refElement) return refElement

  // Fallback to querySelector
  return document.querySelector(`input[data-current-pin-index="${index}"]`) as HTMLInputElement
}

const getConfirmPinInput = (index: number): HTMLInputElement | null => {
  // Get confirm PIN input by template ref array
  const refElement = confirmPinInputRefs.value[index]
  if (refElement) return refElement

  // Fallback to querySelector
  return document.querySelector(`input[data-confirm-pin-index="${index}"]`) as HTMLInputElement
}

const createPin = async () => {
  if (pinLoading.value) return

  try {
    pinLoading.value = true
    clearErrors()

    const pin = newPinDigits.value.join('')

    // Validate PIN
    if (pin.length !== 6) {
      throw new Error('PIN must be exactly 6 digits')
    }

    if (!/^\d{6}$/.test(pin)) {
      throw new Error('PIN must contain only numbers')
    }

    // Check for weak patterns
    const weakPatterns = ['123456', '654321', '111111', '222222', '333333', '444444', '555555', '666666', '777777', '888888', '999999', '000000']
    if (weakPatterns.includes(pin)) {
      throw new Error('Please choose a more secure PIN. Avoid simple patterns.')
    }

    // Create PIN via API
    const result = await authService.createPin(pin)

    if (result.success) {
      hasPinSetup.value = true
      showPinSetup.value = false
      clearPinInputs()
      // Show success message
      alert('PIN created successfully!')
    } else {
      throw new Error('Failed to create PIN')
    }
  } catch (error: any) {
    pinError.value = error.message || 'Failed to create PIN'
  } finally {
    pinLoading.value = false
  }
}

const changePin = async () => {
  if (pinLoading.value) return

  try {
    pinLoading.value = true
    clearErrors()

    const currentPin = currentPinDigits.value.join('')
    const newPin = newPinDigits.value.join('')
    const confirmPin = confirmPinDigits.value.join('')

    // Validate inputs
    if (currentPin.length !== 6) {
      throw new Error('Please enter your current 6-digit PIN')
    }
    if (newPin.length !== 6) {
      throw new Error('Please enter a new 6-digit PIN')
    }
    if (confirmPin.length !== 6) {
      throw new Error('Please confirm your new 6-digit PIN')
    }
    if (newPin !== confirmPin) {
      throw new Error('New PIN and confirmation do not match')
    }
    if (currentPin === newPin) {
      throw new Error('New PIN must be different from current PIN')
    }

    // Validate new PIN strength
    if (!/^\d{6}$/.test(newPin)) {
      throw new Error('New PIN must contain only numbers')
    }

    const weakPatterns = ['123456', '654321', '111111', '222222', '333333', '444444', '555555', '666666', '777777', '888888', '999999', '000000']
    if (weakPatterns.includes(newPin)) {
      throw new Error('Please choose a more secure PIN. Avoid simple patterns.')
    }

    // Update PIN via API
    const result = await authService.updatePin(currentPin, newPin)

    if (result.success) {
      showPinChange.value = false
      clearPinInputs()
      await checkAuthMethods()
      alert('PIN updated successfully!')
    } else {
      throw new Error(result.message || 'Failed to update PIN')
    }
  } catch (error: any) {
    pinError.value = error.message || 'Failed to update PIN'
  } finally {
    pinLoading.value = false
  }
}

const removePin = async () => {
  if (pinLoading.value) return

  try {
    pinLoading.value = true
    clearErrors()

    const currentPin = currentPinDigits.value.join('')

    // Validate input
    if (currentPin.length !== 6) {
      throw new Error('Please enter your current 6-digit PIN to confirm removal')
    }

    const result = await authService.removePin(currentPin)

    if (result.success) {
      showPinRemove.value = false
      clearPinInputs()
      await checkAuthMethods()
      alert('PIN removed successfully!')
    } else {
      throw new Error(result.message || 'Failed to remove PIN')
    }
  } catch (error: any) {
    pinError.value = error.message || 'Failed to remove PIN'
  } finally {
    pinLoading.value = false
  }
}

const setupBiometric = async () => {
  if (biometricLoading.value || !biometricAvailable.value) return

  try {
    biometricLoading.value = true
    clearErrors()

    const result = await authStore.setupBiometric()

    if (result.success) {
      hasBiometricSetup.value = true
      alert('Biometric authentication set up successfully!')
    } else {
      throw new Error('Failed to set up biometric authentication')
    }
  } catch (error: any) {
    let errorMessage = 'Failed to set up biometric authentication'

    if (error.name === 'NotAllowedError') {
      errorMessage = 'Biometric authentication was cancelled or denied'
    } else if (error.name === 'NotSupportedError') {
      errorMessage = 'Biometric authentication is not supported on this device'
    } else if (error.name === 'SecurityError') {
      errorMessage = 'Security error: Please ensure you\'re using HTTPS'
    } else if (error.message) {
      errorMessage = error.message
    }

    biometricError.value = errorMessage
  } finally {
    biometricLoading.value = false
  }
}

const testBiometric = async () => {
  if (biometricLoading.value) return

  try {
    biometricLoading.value = true
    clearErrors()

    const result = await authStore.loginWithBiometric()

    if (result.success) {
      alert('Biometric authentication test successful!')
    } else {
      throw new Error('Biometric authentication test failed')
    }
  } catch (error: any) {
    biometricError.value = error.message || 'Biometric test failed'
  } finally {
    biometricLoading.value = false
  }
}

const removeBiometricAuth = async () => {
  if (!confirm('Are you sure you want to remove biometric authentication?')) return

  try {
    biometricLoading.value = true
    clearErrors()

    const result = await authService.removeBiometric()

    if (result.success) {
      hasBiometricSetup.value = false
      alert('Biometric authentication removed successfully!')
    } else {
      throw new Error('Failed to remove biometric authentication')
    }
  } catch (error: any) {
    biometricError.value = error.message || 'Failed to remove biometric authentication'
  } finally {
    biometricLoading.value = false
  }
}

const savePreferences = async () => {
  try {
    preferencesLoading.value = true

    // Save preferences via API
    const result = await authService.updateSecurityPreferences(preferences)

    if (result.success) {
      alert('Security preferences saved successfully!')
    } else {
      throw new Error('Failed to save preferences')
    }
  } catch (error: any) {
    alert(error.message || 'Failed to save preferences')
  } finally {
    preferencesLoading.value = false
  }
}

const checkAuthMethods = async () => {
  try {
    console.log('Checking auth methods...')
    const data = await authService.getAuthMethods()
    console.log('Auth methods response:', data)

    // Set the values from the API response
    hasPinSetup.value = Boolean(data.hasPin)
    hasBiometricSetup.value = Boolean(data.biometricCount && data.biometricCount > 0)
    console.log('PIN setup:', hasPinSetup.value, 'Biometric setup:', hasBiometricSetup.value)
  } catch (error) {
    console.error('Could not check auth methods:', error)
    // Set defaults to show setup options when API fails
    hasPinSetup.value = false
    hasBiometricSetup.value = false
    console.log('Using defaults - PIN setup:', hasPinSetup.value, 'Biometric setup:', hasBiometricSetup.value)
  }
}

const checkBiometricAvailability = async () => {
  try {
    biometricAvailable.value = await biometricService.isAvailable()
  } catch (error) {
    console.warn('Could not check biometric availability:', error)
    biometricAvailable.value = false
  }
}

// Lifecycle
onMounted(async () => {
  console.log('UserSecuritySettings component mounted')
  await checkBiometricAvailability()
  await checkAuthMethods()
})
</script>

<style scoped>
.user-security-settings {
  max-width: 4xl;
  margin: 0 auto;
}
</style>
