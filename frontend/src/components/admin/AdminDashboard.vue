<template>
  <div class="admin-dashboard space-y-6">
    <div v-if="isLoading" class="flex items-center justify-center min-h-96">
      <div class="text-center">
        <div class="loading loading-spinner loading-lg text-primary"></div>
        <p class="mt-4 text-base-content/70">Loading dashboard data...</p>
      </div>
    </div>

    <div v-else-if="error" class="alert alert-error">
      <div>
        <h3 class="font-bold">Failed to load dashboard</h3>
        <div class="text-sm">{{ error }}</div>
      </div>
      <button @click="fetchDashboardData" class="btn btn-sm btn-outline">
        Retry
      </button>
    </div>

    <div v-else class="space-y-8">
      <div class="bg-gradient-to-r from-primary/10 via-secondary/10 to-accent/10 rounded-2xl p-6 border border-primary/20">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-3xl font-bold bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
              Admin Dashboard
            </h1>
            <p class="text-base-content/70 mt-2">Real-time system monitoring and management</p>
          </div>
          <div class="flex items-center space-x-4">
            <div class="flex items-center space-x-2">
              <div class="w-3 h-3 bg-success rounded-full animate-pulse"></div>
              <span class="text-sm font-medium text-success">System Online</span>
            </div>
            <div class="flex items-center space-x-3">
              <button
                @click="fetchDashboardData"
                class="btn btn-sm btn-ghost"
                :class="{ 'loading': isLoading }"
                :disabled="isLoading"
              >
                <span v-if="!isLoading">🔄 Refresh</span>
              </button>
              <div class="text-right">
                <div class="text-sm text-base-content/70">Last Updated</div>
                <div class="text-sm font-medium">{{ formatTime(systemMetrics.lastUpdated) }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="space-y-4">
        <h2 class="text-2xl font-bold text-base-content">System Overview</h2>
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
          <div class="card bg-gradient-to-br from-success/10 to-success/5 border border-success/20 shadow-lg">
            <div class="card-body">
              <div class="flex items-center justify-between">
                <div>
                  <h3 class="text-lg font-semibold text-success">System Health</h3>
                  <div class="text-3xl font-bold text-success mt-2">{{ systemMetrics.health.toFixed(1) }}%</div>
                  <div class="text-sm text-success/70 font-medium">{{ systemMetrics.status }}</div>
                </div>
              </div>
            </div>
          </div>

          <div class="card bg-gradient-to-br from-primary/10 to-primary/5 border border-primary/20 shadow-lg">
            <div class="card-body">
              <div class="flex items-center justify-between">
                <div>
                  <h3 class="text-lg font-semibold text-primary">Active Users</h3>
                  <div class="text-3xl font-bold text-primary mt-2">{{ systemMetrics.activeUsers.toLocaleString() }}</div>
                  <div class="text-sm text-primary/70 font-medium">{{ systemMetrics.totalUsers.toLocaleString() }} total users</div>
                </div>
              </div>
            </div>
          </div>

          <div class="card bg-gradient-to-br from-secondary/10 to-secondary/5 border border-secondary/20">
            <div class="card-body">
              <div class="flex items-center justify-between">
                <div>
                  <h3 class="text-lg font-semibold text-secondary">Socket Connections</h3>
                  <div class="text-3xl font-bold text-secondary">{{ systemMetrics.socketConnections }}</div>
                  <div class="text-sm text-secondary/70">Real-time active</div>
                </div>
              </div>
            </div>
          </div>

          <div class="card bg-gradient-to-br from-warning/10 to-warning/5 border border-warning/20">
            <div class="card-body">
              <div class="flex items-center justify-between">
                <div>
                  <h3 class="text-lg font-semibold text-warning">Memory Usage</h3>
                  <div class="text-3xl font-bold text-warning">{{ systemMetrics.memoryUsage.toFixed(2) }}%</div>
                  <div class="text-sm text-warning/70">{{ systemMetrics.memoryUsed.toFixed(2) }}GB used</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="space-y-4">
        <h2 class="text-2xl font-bold text-base-content">Quick Actions</h2>
        <div class="card bg-gradient-to-br from-base-100 to-base-200/50 shadow-xl border border-base-300/50">
          <div class="card-body">
            <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
              <button
                v-for="action in quickActions"
                :key="action.id"
                @click="executeAction(action)"
                :data-action="action.action"
                class="btn btn-outline flex-col h-20 p-2 transition-all duration-200 hover:scale-105"
                :class="action.color"
              >
                <span class="text-xs mt-1">{{ action.label }}</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { adminService, type DashboardStats, type SystemHealth, type SocketMetrics } from '@/services/admin'

const isLoading = ref(true)
const error = ref<string | null>(null)

const systemMetrics = reactive({
  health: 0,
  status: 'Loading...',
  cpu: 0,
  activeUsers: 0,
  totalUsers: 0,
  socketConnections: 0,
  memoryUsage: 0,
  memoryUsed: 0,
  uptime: 0,
  lastUpdated: new Date()
})

const quickActions = ref([
  { id: 1, label: 'Restart Server', color: 'btn-warning', action: 'restart_server' },
  { id: 2, label: 'Clear Cache', color: 'btn-info', action: 'clear_cache' },
  { id: 3, label: 'Backup DB', color: 'btn-success', action: 'backup_db' },
  { id: 4, label: 'View Logs', color: 'btn-neutral', action: 'view_logs' },
  { id: 5, label: 'Security Scan', color: 'btn-error', action: 'security_scan' },
  { id: 6, label: 'Performance', color: 'btn-primary', action: 'performance' }
])

const formatTime = (timestamp: Date) => {
  return timestamp.toLocaleTimeString()
}

const executeAction = async (action: any) => {
  try {
    console.log(`🚀 Executing action: ${action.action}`)

    // Show loading state on the button
    const actionButton = document.querySelector(`[data-action="${action.action}"]`)
    if (actionButton) {
      actionButton.classList.add('loading')
    }

    // Execute real API call
    const result = await adminService.executeQuickAction(action.action)

    console.log('✅ Action result:', result)

    // Show success message
    alert(`✅ ${result.result.message || `${action.label} executed successfully!`}`)

    // Refresh dashboard data after certain actions that might affect system state
    if (['clear_cache', 'backup_db', 'restart_server'].includes(action.action)) {
      console.log('🔄 Refreshing dashboard data after action...')
      await fetchDashboardData()
    }

  } catch (error: any) {
    console.error('❌ Action execution failed:', error)
    alert(`❌ Failed to execute ${action.label}: ${error.message || 'Unknown error'}`)
  } finally {
    // Remove loading state from the button
    const actionButton = document.querySelector(`[data-action="${action.action}"]`)
    if (actionButton) {
      actionButton.classList.remove('loading')
    }
  }
}

const fetchDashboardData = async () => {
  try {
    isLoading.value = true
    error.value = null

    console.log('🔄 Fetching dashboard data...')

    // Fetch all data in parallel for better performance
    const [dashboardStats, systemHealth, socketMetrics] = await Promise.all([
      adminService.getDashboardStats(),
      adminService.getSystemHealth(),
      adminService.getSocketMetrics()
    ])

    console.log('📊 Dashboard Stats:', dashboardStats)
    console.log('🏥 System Health:', systemHealth)
    console.log('🔌 Socket Metrics:', socketMetrics)

    // Update system metrics with real data
    // User data from dashboard stats
    systemMetrics.totalUsers = dashboardStats.overview.totalUsers
    systemMetrics.activeUsers = dashboardStats.overview.activeUsers

    // Socket connections from socket metrics
    systemMetrics.socketConnections = socketMetrics.activeConnections

    // System health calculation
    systemMetrics.health = systemHealth.health.status === 'healthy' ? 95 :
                           systemHealth.health.status === 'warning' ? 75 : 50
    systemMetrics.status = systemHealth.health.status === 'healthy' ? 'Excellent' :
                           systemHealth.health.status === 'warning' ? 'Good' : 'Poor'

    // Memory usage from system health
    systemMetrics.memoryUsage = Math.round((systemHealth.system.memory.heapUsed / systemHealth.system.memory.heapTotal) * 100)
    systemMetrics.memoryUsed = Math.round(systemHealth.system.memory.heapUsed / 1024 / 1024 / 1024 * 100) / 100 // Convert to GB

    // CPU usage (convert from microseconds to percentage)
    systemMetrics.cpu = Math.round((systemHealth.system.cpu.user + systemHealth.system.cpu.system) / 1000000 * 100) / 100

    // System uptime
    systemMetrics.uptime = systemHealth.system.uptime
    systemMetrics.lastUpdated = new Date()

    console.log('✅ Dashboard data updated:', systemMetrics)

  } catch (err: any) {
    console.error('❌ Failed to fetch dashboard data:', err)
    error.value = err.message || 'Failed to load dashboard data'
  } finally {
    isLoading.value = false
  }
}

// Auto-refresh interval
let refreshInterval: number | null = null

// Lifecycle hooks
onMounted(async () => {
  console.log('🎯 AdminDashboard mounted - starting data fetch')

  // Initial data fetch
  await fetchDashboardData()

  // Set up auto-refresh every 30 seconds for real-time updates
  refreshInterval = setInterval(async () => {
    console.log('🔄 Auto-refreshing dashboard data...')
    await fetchDashboardData()
  }, 30000) // 30 seconds

  console.log('✅ Auto-refresh interval set up (30s)')
})

onUnmounted(() => {
  console.log('🛑 AdminDashboard unmounted - cleaning up')

  // Clear the refresh interval
  if (refreshInterval) {
    clearInterval(refreshInterval)
    refreshInterval = null
    console.log('✅ Auto-refresh interval cleared')
  }
})
</script>

<style scoped>
.card-header {
  @apply flex items-center justify-between p-6 border-b border-base-200;
}

.card-title {
  @apply text-lg font-semibold;
}
</style>
