<template>
  <div class="admin-dashboard space-y-6">
    <!-- Real-time System Status -->
    <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
      <!-- System Health -->
      <div class="card bg-gradient-to-br from-success/10 to-success/5 border border-success/20">
        <div class="card-body">
          <div class="flex items-center justify-between">
            <div>
              <h3 class="text-lg font-semibold text-success">System Health</h3>
              <div class="text-3xl font-bold text-success">{{ systemMetrics.health.toFixed(2) }}%</div>
              <div class="text-sm text-success/70">{{ systemMetrics.status }}</div>
            </div>
            <div class="p-3 bg-success/20 rounded-full">
              <Icon name="heart" size="xl" class="text-success" />
            </div>
          </div>
          <div class="mt-4">
            <div class="flex justify-between text-xs mb-1">
              <span>CPU</span>
              <span>{{ systemMetrics.cpu.toFixed(2) }}%</span>
            </div>
            <progress class="progress progress-success w-full" :value="systemMetrics.cpu" max="100"></progress>
          </div>
        </div>
      </div>

      <!-- Active Users -->
      <div class="card bg-gradient-to-br from-primary/10 to-primary/5 border border-primary/20">
        <div class="card-body">
          <div class="flex items-center justify-between">
            <div>
              <h3 class="text-lg font-semibold text-primary">Active Users</h3>
              <div class="text-3xl font-bold text-primary">{{ systemMetrics.activeUsers }}</div>
              <div class="text-sm text-primary/70">{{ systemMetrics.totalUsers }} total</div>
            </div>
            <div class="p-3 bg-primary/20 rounded-full">
              <Icon name="users" size="xl" class="text-primary" />
            </div>
          </div>
          <div class="mt-4">
            <div class="flex items-center text-xs">
              <Icon name="trending-up" size="sm" class="text-success mr-1" />
              <span class="text-success">+12% from yesterday</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Socket Connections -->
      <div class="card bg-gradient-to-br from-secondary/10 to-secondary/5 border border-secondary/20">
        <div class="card-body">
          <div class="flex items-center justify-between">
            <div>
              <h3 class="text-lg font-semibold text-secondary">Socket Connections</h3>
              <div class="text-3xl font-bold text-secondary">{{ systemMetrics.socketConnections }}</div>
              <div class="text-sm text-secondary/70">Real-time active</div>
            </div>
            <div class="p-3 bg-secondary/20 rounded-full">
              <Icon name="wifi" size="xl" class="text-secondary" />
            </div>
          </div>
          <div class="mt-4">
            <div class="flex items-center text-xs">
              <div class="w-2 h-2 bg-success rounded-full mr-2 animate-pulse"></div>
              <span>All connections stable</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Memory Usage -->
      <div class="card bg-gradient-to-br from-warning/10 to-warning/5 border border-warning/20">
        <div class="card-body">
          <div class="flex items-center justify-between">
            <div>
              <h3 class="text-lg font-semibold text-warning">Memory Usage</h3>
              <div class="text-3xl font-bold text-warning">{{ systemMetrics.memoryUsage.toFixed(2) }}%</div>
              <div class="text-sm text-warning/70">{{ systemMetrics.memoryUsed.toFixed(2) }}GB used</div>
            </div>
            <div class="p-3 bg-warning/20 rounded-full">
              <Icon name="cpu-chip" size="xl" class="text-warning" />
            </div>
          </div>
          <div class="mt-4">
            <div class="flex justify-between text-xs mb-1">
              <span>RAM</span>
              <span>{{ systemMetrics.memoryUsage.toFixed(2) }}%</span>
            </div>
            <progress class="progress progress-warning w-full" :value="systemMetrics.memoryUsage" max="100"></progress>
          </div>
        </div>
      </div>
    </div>

    <!-- Real-time Activity Feed -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Live Activity -->
      <div class="card bg-base-100 shadow-lg">
        <div class="card-header">
          <h3 class="card-title flex items-center">
            <Icon name="activity" size="md" class="mr-2" />
            Live Activity Feed
          </h3>
          <div class="flex items-center space-x-2">
            <div class="w-2 h-2 bg-success rounded-full animate-pulse"></div>
            <span class="text-xs text-success">Live</span>
          </div>
        </div>
        <div class="card-body p-0">
          <div class="max-h-80 overflow-y-auto">
            <div
              v-for="activity in realtimeActivities"
              :key="activity.id"
              class="flex items-center p-4 border-b border-base-200 hover:bg-base-50 transition-colors"
            >
              <div class="p-2 rounded-full mr-3" :class="getActivityIconClass(activity.type)">
                <Icon :name="getActivityIcon(activity.type)" size="sm" />
              </div>
              <div class="flex-1">
                <div class="font-medium">{{ activity.title }}</div>
                <div class="text-sm text-base-content/70">{{ activity.description }}</div>
                <div class="text-xs text-base-content/50">{{ formatTime(activity.timestamp) }}</div>
              </div>
              <div class="badge badge-sm" :class="getActivityBadgeClass(activity.severity)">
                {{ activity.severity }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- System Alerts -->
      <div class="card bg-base-100 shadow-lg">
        <div class="card-header">
          <h3 class="card-title flex items-center">
            <Icon name="bell" size="md" class="mr-2" />
            System Alerts
          </h3>
          <button @click="clearAllAlerts" class="btn btn-ghost btn-sm">
            Clear All
          </button>
        </div>
        <div class="card-body p-0">
          <div class="max-h-80 overflow-y-auto">
            <div
              v-for="alert in systemAlerts"
              :key="alert.id"
              class="p-4 border-b border-base-200"
              :class="getAlertClass(alert.level)"
            >
              <div class="flex items-start">
                <Icon :name="getAlertIcon(alert.level)" size="sm" class="mt-1 mr-3" />
                <div class="flex-1">
                  <div class="font-medium">{{ alert.title }}</div>
                  <div class="text-sm opacity-80">{{ alert.message }}</div>
                  <div class="text-xs opacity-60 mt-1">{{ formatTime(alert.timestamp) }}</div>
                </div>
                <button @click="dismissAlert(alert.id)" class="btn btn-ghost btn-xs">
                  <Icon name="x" size="sm" />
                </button>
              </div>
            </div>
            <div v-if="systemAlerts.length === 0" class="p-8 text-center text-base-content/50">
              <Icon name="check-circle" size="lg" class="mx-auto mb-2" />
              <div>No active alerts</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Performance Charts -->
    <div class="card bg-base-100 shadow-lg">
      <div class="card-header">
        <h3 class="card-title flex items-center">
          <Icon name="chart-line" size="md" class="mr-2" />
          Performance Metrics
        </h3>
        <div class="flex space-x-2">
          <button
            v-for="period in ['1h', '6h', '24h', '7d']"
            :key="period"
            @click="selectedPeriod = period"
            :class="[
              'btn btn-sm',
              selectedPeriod === period ? 'btn-primary' : 'btn-ghost'
            ]"
          >
            {{ period }}
          </button>
        </div>
      </div>
      <div class="card-body">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- CPU Usage Chart -->
          <div>
            <h4 class="font-semibold mb-3">CPU Usage</h4>
            <div class="h-32 bg-base-200 rounded-lg flex items-center justify-center">
              <span class="text-base-content/50">Chart placeholder</span>
            </div>
          </div>

          <!-- Memory Usage Chart -->
          <div>
            <h4 class="font-semibold mb-3">Memory Usage</h4>
            <div class="h-32 bg-base-200 rounded-lg flex items-center justify-center">
              <span class="text-base-content/50">Chart placeholder</span>
            </div>
          </div>

          <!-- Network Traffic Chart -->
          <div>
            <h4 class="font-semibold mb-3">Network Traffic</h4>
            <div class="h-32 bg-base-200 rounded-lg flex items-center justify-center">
              <span class="text-base-content/50">Chart placeholder</span>
            </div>
          </div>

          <!-- Response Time Chart -->
          <div>
            <h4 class="font-semibold mb-3">API Response Time</h4>
            <div class="h-32 bg-base-200 rounded-lg flex items-center justify-center">
              <span class="text-base-content/50">Chart placeholder</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Quick Actions -->
    <div class="card bg-base-100 shadow-lg">
      <div class="card-header">
        <h3 class="card-title flex items-center">
          <Icon name="bolt" size="md" class="mr-2" />
          Quick Actions
        </h3>
      </div>
      <div class="card-body">
        <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
          <button
            v-for="action in quickActions"
            :key="action.id"
            @click="executeAction(action)"
            class="btn btn-outline flex-col h-20 p-2"
            :class="action.color"
          >
            <Icon :name="action.icon" size="md" />
            <span class="text-xs mt-1">{{ action.label }}</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import Icon from '@/components/common/Icon.vue'

// System metrics
const systemMetrics = reactive({
  health: 98,
  status: 'Excellent',
  cpu: 45,
  activeUsers: 127,
  totalUsers: 1247,
  socketConnections: 89,
  memoryUsage: 67,
  memoryUsed: 2.4
})

// Real-time activities
const realtimeActivities = ref([
  {
    id: 1,
    type: 'user_login',
    title: 'User Login',
    description: '<EMAIL> logged in',
    timestamp: new Date(),
    severity: 'info'
  },
  {
    id: 2,
    type: 'system_update',
    title: 'System Update',
    description: 'Database backup completed successfully',
    timestamp: new Date(Date.now() - 300000),
    severity: 'success'
  },
  {
    id: 3,
    type: 'security_alert',
    title: 'Security Alert',
    description: 'Multiple failed login attempts detected',
    timestamp: new Date(Date.now() - 600000),
    severity: 'warning'
  }
])

// System alerts
const systemAlerts = ref([
  {
    id: 1,
    level: 'warning',
    title: 'High Memory Usage',
    message: 'Memory usage is above 80%. Consider optimizing or scaling.',
    timestamp: new Date(Date.now() - 900000)
  },
  {
    id: 2,
    level: 'info',
    title: 'Scheduled Maintenance',
    message: 'System maintenance scheduled for tonight at 2:00 AM.',
    timestamp: new Date(Date.now() - 1800000)
  }
])

// Chart period
const selectedPeriod = ref('24h')

// Quick actions
const quickActions = ref([
  { id: 1, label: 'Restart Server', icon: 'refresh', color: 'btn-warning', action: 'restart_server' },
  { id: 2, label: 'Clear Cache', icon: 'trash', color: 'btn-info', action: 'clear_cache' },
  { id: 3, label: 'Backup DB', icon: 'database', color: 'btn-success', action: 'backup_db' },
  { id: 4, label: 'View Logs', icon: 'document', color: 'btn-neutral', action: 'view_logs' },
  { id: 5, label: 'Security Scan', icon: 'shield', color: 'btn-error', action: 'security_scan' },
  { id: 6, label: 'Performance', icon: 'chart-bar', color: 'btn-primary', action: 'performance' }
])

// Update interval
let updateInterval: number | null = null

// Methods
const getActivityIconClass = (type: string) => {
  switch (type) {
    case 'user_login': return 'bg-primary/20 text-primary'
    case 'system_update': return 'bg-success/20 text-success'
    case 'security_alert': return 'bg-warning/20 text-warning'
    default: return 'bg-base-300 text-base-content'
  }
}

const getActivityIcon = (type: string) => {
  switch (type) {
    case 'user_login': return 'user'
    case 'system_update': return 'cog'
    case 'security_alert': return 'shield'
    default: return 'info'
  }
}

const getActivityBadgeClass = (severity: string) => {
  switch (severity) {
    case 'success': return 'badge-success'
    case 'warning': return 'badge-warning'
    case 'error': return 'badge-error'
    default: return 'badge-info'
  }
}

const getAlertClass = (level: string) => {
  switch (level) {
    case 'error': return 'bg-error/10 border-l-4 border-error'
    case 'warning': return 'bg-warning/10 border-l-4 border-warning'
    case 'info': return 'bg-info/10 border-l-4 border-info'
    default: return 'bg-base-200'
  }
}

const getAlertIcon = (level: string) => {
  switch (level) {
    case 'error': return 'x-circle'
    case 'warning': return 'exclamation-triangle'
    case 'info': return 'info'
    default: return 'bell'
  }
}

const formatTime = (timestamp: Date) => {
  const now = new Date()
  const diff = now.getTime() - timestamp.getTime()
  const minutes = Math.floor(diff / 60000)
  
  if (minutes < 1) return 'Just now'
  if (minutes < 60) return `${minutes}m ago`
  
  const hours = Math.floor(minutes / 60)
  if (hours < 24) return `${hours}h ago`
  
  const days = Math.floor(hours / 24)
  return `${days}d ago`
}

const dismissAlert = (alertId: number) => {
  const index = systemAlerts.value.findIndex(alert => alert.id === alertId)
  if (index > -1) {
    systemAlerts.value.splice(index, 1)
  }
}

const clearAllAlerts = () => {
  systemAlerts.value = []
}

const executeAction = (action: any) => {
  console.log(`Executing action: ${action.action}`)
  // Implement action handlers
  switch (action.action) {
    case 'restart_server':
      alert('Server restart initiated')
      break
    case 'clear_cache':
      alert('Cache cleared successfully')
      break
    case 'backup_db':
      alert('Database backup started')
      break
    case 'view_logs':
      alert('Opening system logs')
      break
    case 'security_scan':
      alert('Security scan initiated')
      break
    case 'performance':
      alert('Opening performance monitor')
      break
  }
}

const updateMetrics = () => {
  // Simulate real-time updates
  systemMetrics.cpu = Math.max(20, Math.min(90, systemMetrics.cpu + (Math.random() - 0.5) * 10))
  systemMetrics.memoryUsage = Math.max(30, Math.min(95, systemMetrics.memoryUsage + (Math.random() - 0.5) * 5))
  systemMetrics.activeUsers = Math.max(50, Math.min(200, systemMetrics.activeUsers + Math.floor((Math.random() - 0.5) * 10)))
  systemMetrics.socketConnections = Math.max(20, Math.min(150, systemMetrics.socketConnections + Math.floor((Math.random() - 0.5) * 5)))
  
  // Update health based on metrics
  const avgMetric = (systemMetrics.cpu + systemMetrics.memoryUsage) / 2
  systemMetrics.health = Math.max(70, Math.min(100, 100 - avgMetric * 0.3))
  
  if (systemMetrics.health > 90) {
    systemMetrics.status = 'Excellent'
  } else if (systemMetrics.health > 80) {
    systemMetrics.status = 'Good'
  } else if (systemMetrics.health > 70) {
    systemMetrics.status = 'Fair'
  } else {
    systemMetrics.status = 'Poor'
  }
}

// Lifecycle
onMounted(() => {
  // Start real-time updates
  updateInterval = setInterval(updateMetrics, 5000)

  // Initial update
  updateMetrics()
})

onUnmounted(() => {
  if (updateInterval) {
    clearInterval(updateInterval)
    updateInterval = null
  }
})
</script>

<style scoped>
.card-header {
  @apply flex items-center justify-between p-6 border-b border-base-200;
}

.card-title {
  @apply text-lg font-semibold;
}

/* Custom scrollbar */
.overflow-y-auto::-webkit-scrollbar {
  width: 4px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: transparent;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: hsl(var(--bc) / 0.2);
  border-radius: 2px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--bc) / 0.3);
}
</style>
