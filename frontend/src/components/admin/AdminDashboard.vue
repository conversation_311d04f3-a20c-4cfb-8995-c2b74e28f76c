<template>
  <div class="admin-dashboard space-y-6">
    <!-- Loading State -->
    <div v-if="isLoading" class="flex items-center justify-center min-h-96">
      <div class="text-center">
        <div class="loading loading-spinner loading-lg text-primary"></div>
        <p class="mt-4 text-base-content/70">Loading dashboard data...</p>
      </div>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="alert alert-error">
      <Icon name="x-circle" size="md" />
      <div>
        <h3 class="font-bold">Failed to load dashboard</h3>
        <div class="text-sm">{{ error }}</div>
      </div>
      <button @click="fetchDashboardData" class="btn btn-sm btn-outline">
        <Icon name="refresh" size="sm" class="mr-1" />
        Retry
      </button>
    </div>

    <!-- Dashboard Content -->
    <div v-else class="space-y-8">
      <!-- Dashboard Header -->
      <div class="bg-gradient-to-r from-primary/10 via-secondary/10 to-accent/10 rounded-2xl p-6 border border-primary/20">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-3xl font-bold bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
              Admin Dashboard
            </h1>
            <p class="text-base-content/70 mt-2">Real-time system monitoring and management</p>
          </div>
          <div class="flex items-center space-x-4">
            <div class="flex items-center space-x-2">
              <div class="w-3 h-3 bg-success rounded-full animate-pulse"></div>
              <span class="text-sm font-medium text-success">System Online</span>
            </div>
            <div class="text-right">
              <div class="text-sm text-base-content/70">Last Updated</div>
              <div class="text-sm font-medium">{{ formatTime(new Date()) }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Real-time System Status -->
      <div class="space-y-4">
        <h2 class="text-2xl font-bold text-base-content flex items-center">
          <Icon name="chart-bar" size="lg" class="mr-3 text-primary" />
          System Overview
        </h2>
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
      <!-- System Health -->
      <div class="card bg-gradient-to-br from-success/10 to-success/5 border border-success/20 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-[1.02]">
        <div class="card-body">
          <div class="flex items-center justify-between">
            <div>
              <h3 class="text-lg font-semibold text-success flex items-center">
                <Icon name="heart" size="sm" class="mr-2" />
                System Health
              </h3>
              <div class="text-3xl font-bold text-success mt-2">{{ systemMetrics.health.toFixed(1) }}%</div>
              <div class="text-sm text-success/70 font-medium">{{ systemMetrics.status }}</div>
            </div>
            <div class="p-4 bg-success/20 rounded-2xl shadow-inner">
              <Icon name="heart" size="2xl" class="text-success" />
            </div>
          </div>
          <div class="mt-6 space-y-3">
            <div class="flex justify-between text-xs font-medium">
              <span class="text-base-content/70">CPU Usage</span>
              <span class="text-success">{{ systemMetrics.cpu.toFixed(1) }}%</span>
            </div>
            <div class="w-full bg-success/20 rounded-full h-2">
              <div
                class="bg-gradient-to-r from-success to-success/80 h-2 rounded-full transition-all duration-500"
                :style="{ width: `${systemMetrics.cpu}%` }"
              ></div>
            </div>
          </div>
        </div>
      </div>

      <!-- Active Users -->
      <div class="card bg-gradient-to-br from-primary/10 to-primary/5 border border-primary/20 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-[1.02]">
        <div class="card-body">
          <div class="flex items-center justify-between">
            <div>
              <h3 class="text-lg font-semibold text-primary flex items-center">
                <Icon name="users" size="sm" class="mr-2" />
                Active Users
              </h3>
              <div class="text-3xl font-bold text-primary mt-2">{{ systemMetrics.activeUsers.toLocaleString() }}</div>
              <div class="text-sm text-primary/70 font-medium">{{ systemMetrics.totalUsers.toLocaleString() }} total users</div>
            </div>
            <div class="p-4 bg-primary/20 rounded-2xl shadow-inner">
              <Icon name="users" size="2xl" class="text-primary" />
            </div>
          </div>
          <div class="mt-6">
            <div class="flex items-center justify-between">
              <div class="flex items-center text-xs font-medium">
                <Icon name="trending-up" size="sm" class="text-success mr-2" />
                <span class="text-success">+{{ Math.round((systemMetrics.activeUsers / systemMetrics.totalUsers) * 100) }}% active</span>
              </div>
              <div class="text-xs text-primary/70">
                {{ Math.round((systemMetrics.activeUsers / systemMetrics.totalUsers) * 100) }}% engagement
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Socket Connections -->
      <div class="card bg-gradient-to-br from-secondary/10 to-secondary/5 border border-secondary/20">
        <div class="card-body">
          <div class="flex items-center justify-between">
            <div>
              <h3 class="text-lg font-semibold text-secondary">Socket Connections</h3>
              <div class="text-3xl font-bold text-secondary">{{ systemMetrics.socketConnections }}</div>
              <div class="text-sm text-secondary/70">Real-time active</div>
            </div>
            <div class="p-3 bg-secondary/20 rounded-full">
              <Icon name="wifi" size="xl" class="text-secondary" />
            </div>
          </div>
          <div class="mt-4">
            <div class="flex items-center text-xs">
              <div class="w-2 h-2 bg-success rounded-full mr-2 animate-pulse"></div>
              <span>All connections stable</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Memory Usage -->
      <div class="card bg-gradient-to-br from-warning/10 to-warning/5 border border-warning/20">
        <div class="card-body">
          <div class="flex items-center justify-between">
            <div>
              <h3 class="text-lg font-semibold text-warning">Memory Usage</h3>
              <div class="text-3xl font-bold text-warning">{{ systemMetrics.memoryUsage.toFixed(2) }}%</div>
              <div class="text-sm text-warning/70">{{ systemMetrics.memoryUsed.toFixed(2) }}GB used</div>
            </div>
            <div class="p-3 bg-warning/20 rounded-full">
              <Icon name="cpu-chip" size="xl" class="text-warning" />
            </div>
          </div>
          <div class="mt-4">
            <div class="flex justify-between text-xs mb-1">
              <span>RAM</span>
              <span>{{ systemMetrics.memoryUsage.toFixed(2) }}%</span>
            </div>
            <progress class="progress progress-warning w-full" :value="systemMetrics.memoryUsage" max="100"></progress>
          </div>
        </div>
      </div>
      </div>
    </div>

      <!-- Real-time Activity & Monitoring -->
      <div class="space-y-4">
        <h2 class="text-2xl font-bold text-base-content flex items-center">
          <Icon name="activity" size="lg" class="mr-3 text-secondary" />
          Live Monitoring
        </h2>
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Live Activity -->
      <div class="card bg-gradient-to-br from-base-100 to-base-200/50 shadow-xl border border-base-300/50 hover:shadow-2xl transition-all duration-300">
        <div class="card-header bg-gradient-to-r from-info/10 to-info/5 border-b border-info/20">
          <h3 class="card-title flex items-center text-info">
            <Icon name="activity" size="md" class="mr-3" />
            Live Activity Feed
          </h3>
          <div class="flex items-center space-x-3">
            <div class="flex items-center space-x-2">
              <div class="w-3 h-3 bg-success rounded-full animate-pulse shadow-lg"></div>
              <span class="text-sm font-medium text-success">Live</span>
            </div>
            <div class="badge badge-info badge-sm">{{ realtimeActivities.length }} events</div>
          </div>
        </div>
        <div class="card-body p-0">
          <div class="max-h-80 overflow-y-auto">
            <div
              v-for="activity in realtimeActivities"
              :key="activity.id"
              class="flex items-center p-4 border-b border-base-200 hover:bg-base-50 transition-colors"
            >
              <div class="p-2 rounded-full mr-3" :class="getActivityIconClass(activity.type)">
                <Icon :name="getActivityIcon(activity.type)" size="sm" />
              </div>
              <div class="flex-1">
                <div class="font-medium">{{ activity.title }}</div>
                <div class="text-sm text-base-content/70">{{ activity.description }}</div>
                <div class="text-xs text-base-content/50">{{ formatTime(activity.timestamp) }}</div>
              </div>
              <div class="badge badge-sm" :class="getActivityBadgeClass(activity.severity)">
                {{ activity.severity }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- System Alerts -->
      <div class="card bg-base-100 shadow-lg">
        <div class="card-header">
          <h3 class="card-title flex items-center">
            <Icon name="bell" size="md" class="mr-2" />
            System Alerts
          </h3>
          <button @click="clearAllAlerts" class="btn btn-ghost btn-sm">
            Clear All
          </button>
        </div>
        <div class="card-body p-0">
          <div class="max-h-80 overflow-y-auto">
            <div
              v-for="alert in systemAlerts"
              :key="alert.id"
              class="p-4 border-b border-base-200"
              :class="getAlertClass(alert.level)"
            >
              <div class="flex items-start">
                <Icon :name="getAlertIcon(alert.level)" size="sm" class="mt-1 mr-3" />
                <div class="flex-1">
                  <div class="font-medium">{{ alert.title }}</div>
                  <div class="text-sm opacity-80">{{ alert.message }}</div>
                  <div class="text-xs opacity-60 mt-1">{{ formatTime(alert.timestamp) }}</div>
                </div>
                <button @click="dismissAlert(alert.id)" class="btn btn-ghost btn-xs">
                  <Icon name="x" size="sm" />
                </button>
              </div>
            </div>
            <div v-if="systemAlerts.length === 0" class="p-8 text-center text-base-content/50">
              <Icon name="check-circle" size="lg" class="mx-auto mb-2" />
              <div>No active alerts</div>
            </div>
          </div>
        </div>
      </div>
      </div>

      <!-- Performance Analytics -->
      <div class="space-y-4">
        <h2 class="text-2xl font-bold text-base-content flex items-center">
          <Icon name="chart-line" size="lg" class="mr-3 text-accent" />
          Performance Analytics
        </h2>
        <div class="card bg-gradient-to-br from-base-100 to-base-200/50 shadow-xl border border-base-300/50">
      <div class="card-header">
        <h3 class="card-title flex items-center">
          <Icon name="chart-line" size="md" class="mr-2" />
          Performance Metrics
        </h3>
        <div class="flex space-x-2">
          <button
            v-for="period in ['1h', '6h', '24h', '7d']"
            :key="period"
            @click="selectedPeriod = period"
            :class="[
              'btn btn-sm',
              selectedPeriod === period ? 'btn-primary' : 'btn-ghost'
            ]"
          >
            {{ period }}
          </button>
        </div>
      </div>
      <div class="card-body">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- CPU Usage Chart -->
          <div class="bg-gradient-to-br from-warning/5 to-warning/10 p-4 rounded-xl border border-warning/20">
            <h4 class="font-semibold mb-3 flex items-center text-warning">
              <Icon name="cpu-chip" size="sm" class="mr-2" />
              CPU Usage
            </h4>
            <div class="h-32 bg-warning/10 rounded-lg flex items-center justify-center relative overflow-hidden">
              <div class="absolute inset-0 flex items-end justify-around p-2">
                <div
                  v-for="i in 12"
                  :key="i"
                  class="bg-gradient-to-t from-warning to-warning/60 rounded-t w-4 transition-all duration-500"
                  :style="{ height: `${Math.random() * 80 + 20}%` }"
                ></div>
              </div>
              <div class="relative z-10 text-center">
                <div class="text-2xl font-bold text-warning">{{ systemMetrics.cpu.toFixed(1) }}%</div>
                <div class="text-xs text-warning/70">Current Usage</div>
              </div>
            </div>
          </div>

          <!-- Memory Usage Chart -->
          <div class="bg-gradient-to-br from-error/5 to-error/10 p-4 rounded-xl border border-error/20">
            <h4 class="font-semibold mb-3 flex items-center text-error">
              <Icon name="server" size="sm" class="mr-2" />
              Memory Usage
            </h4>
            <div class="h-32 bg-error/10 rounded-lg flex items-center justify-center relative">
              <div class="w-24 h-24 relative">
                <svg class="w-full h-full transform -rotate-90" viewBox="0 0 36 36">
                  <path
                    d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-dasharray="100, 100"
                    class="text-error/20"
                  />
                  <path
                    d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    :stroke-dasharray="`${systemMetrics.memoryUsage}, 100`"
                    class="text-error"
                  />
                </svg>
                <div class="absolute inset-0 flex items-center justify-center">
                  <div class="text-center">
                    <div class="text-lg font-bold text-error">{{ systemMetrics.memoryUsage.toFixed(1) }}%</div>
                    <div class="text-xs text-error/70">{{ systemMetrics.memoryUsed.toFixed(1) }}GB</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Socket Connections Chart -->
          <div class="bg-gradient-to-br from-info/5 to-info/10 p-4 rounded-xl border border-info/20">
            <h4 class="font-semibold mb-3 flex items-center text-info">
              <Icon name="wifi" size="sm" class="mr-2" />
              Socket Connections
            </h4>
            <div class="h-32 bg-info/10 rounded-lg flex items-center justify-center">
              <div class="text-center">
                <div class="text-3xl font-bold text-info mb-2">{{ systemMetrics.socketConnections }}</div>
                <div class="flex items-center justify-center space-x-4 text-xs">
                  <div class="flex items-center">
                    <div class="w-2 h-2 bg-success rounded-full mr-1 animate-pulse"></div>
                    <span class="text-info/70">Active</span>
                  </div>
                  <div class="flex items-center">
                    <div class="w-2 h-2 bg-info rounded-full mr-1"></div>
                    <span class="text-info/70">Stable</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- System Health Chart -->
          <div class="bg-gradient-to-br from-success/5 to-success/10 p-4 rounded-xl border border-success/20">
            <h4 class="font-semibold mb-3 flex items-center text-success">
              <Icon name="heart" size="sm" class="mr-2" />
              System Health
            </h4>
            <div class="h-32 bg-success/10 rounded-lg flex items-center justify-center">
              <div class="text-center">
                <div class="text-3xl font-bold text-success mb-2">{{ systemMetrics.health.toFixed(1) }}%</div>
                <div class="text-sm font-medium text-success/80">{{ systemMetrics.status }}</div>
                <div class="mt-2 flex justify-center">
                  <div class="flex space-x-1">
                    <div
                      v-for="i in 5"
                      :key="i"
                      class="w-2 h-6 rounded-full"
                      :class="i <= (systemMetrics.health / 20) ? 'bg-success' : 'bg-success/20'"
                    ></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      </div>

      <!-- Management Tools -->
      <div class="space-y-4">
        <h2 class="text-2xl font-bold text-base-content flex items-center">
          <Icon name="bolt" size="lg" class="mr-3 text-warning" />
          Quick Actions & Tools
        </h2>
        <div class="card bg-gradient-to-br from-base-100 to-base-200/50 shadow-xl border border-base-300/50">
          <div class="card-header">
            <h3 class="card-title flex items-center">
              <Icon name="bolt" size="md" class="mr-2" />
              Quick Actions
            </h3>
          </div>
          <div class="card-body">
            <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
              <button
                v-for="action in quickActions"
                :key="action.id"
                @click="executeAction(action)"
                :data-action="action.action"
                class="btn btn-outline flex-col h-20 p-2 transition-all duration-200 hover:scale-105"
                :class="action.color"
              >
                <Icon :name="action.icon" size="md" />
                <span class="text-xs mt-1">{{ action.label }}</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div> <!-- End dashboard content -->
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import Icon from '@/components/common/Icon.vue'
import { adminService, type DashboardStats, type SystemHealth, type SocketMetrics } from '@/services/admin'

// Loading states
const isLoading = ref(true)
const error = ref<string | null>(null)

// Real system metrics
const systemMetrics = reactive({
  health: 0,
  status: 'Loading...',
  cpu: 0,
  activeUsers: 0,
  totalUsers: 0,
  socketConnections: 0,
  memoryUsage: 0,
  memoryUsed: 0
})

// Real-time activities (will be populated from real data)
const realtimeActivities = ref<Array<{
  id: number
  type: string
  title: string
  description: string
  timestamp: Date
  severity: string
}>>([])

// System alerts (will be populated from real data)
const systemAlerts = ref<Array<{
  id: number
  level: string
  title: string
  message: string
  timestamp: Date
}>>([])

// Chart period
const selectedPeriod = ref('24h')

// Quick actions
const quickActions = ref([
  { id: 1, label: 'Restart Server', icon: 'refresh', color: 'btn-warning', action: 'restart_server' },
  { id: 2, label: 'Clear Cache', icon: 'trash', color: 'btn-info', action: 'clear_cache' },
  { id: 3, label: 'Backup DB', icon: 'database', color: 'btn-success', action: 'backup_db' },
  { id: 4, label: 'View Logs', icon: 'document', color: 'btn-neutral', action: 'view_logs' },
  { id: 5, label: 'Security Scan', icon: 'shield', color: 'btn-error', action: 'security_scan' },
  { id: 6, label: 'Performance', icon: 'chart-bar', color: 'btn-primary', action: 'performance' }
])

// Update interval
let updateInterval: number | null = null

// Methods
const getActivityIconClass = (type: string) => {
  switch (type) {
    case 'user_login': return 'bg-primary/20 text-primary'
    case 'system_update': return 'bg-success/20 text-success'
    case 'security_alert': return 'bg-warning/20 text-warning'
    default: return 'bg-base-300 text-base-content'
  }
}

const getActivityIcon = (type: string) => {
  switch (type) {
    case 'user_login': return 'user'
    case 'system_update': return 'cog'
    case 'security_alert': return 'shield'
    default: return 'info'
  }
}

const getActivityBadgeClass = (severity: string) => {
  switch (severity) {
    case 'success': return 'badge-success'
    case 'warning': return 'badge-warning'
    case 'error': return 'badge-error'
    default: return 'badge-info'
  }
}

const getAlertClass = (level: string) => {
  switch (level) {
    case 'error': return 'bg-error/10 border-l-4 border-error'
    case 'warning': return 'bg-warning/10 border-l-4 border-warning'
    case 'info': return 'bg-info/10 border-l-4 border-info'
    default: return 'bg-base-200'
  }
}

const getAlertIcon = (level: string) => {
  switch (level) {
    case 'error': return 'x-circle'
    case 'warning': return 'exclamation-triangle'
    case 'info': return 'info'
    default: return 'bell'
  }
}

const formatTime = (timestamp: Date) => {
  const now = new Date()
  const diff = now.getTime() - timestamp.getTime()
  const minutes = Math.floor(diff / 60000)
  
  if (minutes < 1) return 'Just now'
  if (minutes < 60) return `${minutes}m ago`
  
  const hours = Math.floor(minutes / 60)
  if (hours < 24) return `${hours}h ago`
  
  const days = Math.floor(hours / 24)
  return `${days}d ago`
}

const dismissAlert = (alertId: number) => {
  const index = systemAlerts.value.findIndex(alert => alert.id === alertId)
  if (index > -1) {
    systemAlerts.value.splice(index, 1)
  }
}

const clearAllAlerts = () => {
  systemAlerts.value = []
}

const executeAction = async (action: any) => {
  try {
    console.log(`Executing action: ${action.action}`)

    // Show loading state
    const actionButton = document.querySelector(`[data-action="${action.action}"]`)
    if (actionButton) {
      actionButton.classList.add('loading')
    }

    // Execute real API call
    const result = await adminService.executeQuickAction(action.action)

    // Show success message
    alert(`✅ ${result.result.message}`)

    // Refresh dashboard data after certain actions
    if (['clear_cache', 'backup_db'].includes(action.action)) {
      await fetchDashboardData()
    }

  } catch (error: any) {
    console.error('Action execution failed:', error)
    alert(`❌ Failed to execute ${action.label}: ${error.message}`)
  } finally {
    // Remove loading state
    const actionButton = document.querySelector(`[data-action="${action.action}"]`)
    if (actionButton) {
      actionButton.classList.remove('loading')
    }
  }
}

// Data fetching functions
const fetchDashboardData = async () => {
  try {
    isLoading.value = true
    error.value = null

    // Fetch all data in parallel
    const [dashboardStats, systemHealth, socketMetrics, systemAlerts, liveActivity] = await Promise.all([
      adminService.getDashboardStats(),
      adminService.getSystemHealth(),
      adminService.getSocketMetrics(),
      adminService.getSystemAlerts(),
      adminService.getLiveActivity(20)
    ])

    // Update system metrics with real data
    systemMetrics.totalUsers = dashboardStats.overview.totalUsers
    systemMetrics.activeUsers = dashboardStats.overview.activeUsers
    systemMetrics.socketConnections = socketMetrics.activeConnections

    // Calculate health percentage based on system status
    systemMetrics.health = systemHealth.health.status === 'healthy' ? 95 :
                           systemHealth.health.status === 'warning' ? 75 : 50
    systemMetrics.status = systemHealth.health.status === 'healthy' ? 'Excellent' :
                           systemHealth.health.status === 'warning' ? 'Good' : 'Poor'

    // Memory and CPU from system health
    systemMetrics.memoryUsage = Math.round((systemHealth.system.memory.heapUsed / systemHealth.system.memory.heapTotal) * 100)
    systemMetrics.memoryUsed = Math.round(systemHealth.system.memory.heapUsed / 1024 / 1024 / 1024 * 100) / 100 // GB
    systemMetrics.cpu = Math.round((systemHealth.system.cpu.user + systemHealth.system.cpu.system) / 1000000 * 100) // Convert to percentage

    // Use real data instead of generating mock data
    realtimeActivities.value = liveActivity.activities.map(activity => ({
      ...activity,
      timestamp: new Date(activity.timestamp)
    }))

    systemAlerts.value = systemAlerts.alerts.map(alert => ({
      ...alert,
      timestamp: new Date(alert.timestamp)
    }))

  } catch (err: any) {
    console.error('Failed to fetch dashboard data:', err)
    error.value = err.message || 'Failed to load dashboard data'
  } finally {
    isLoading.value = false
  }
}

const generateRealtimeActivities = (stats: DashboardStats, health: SystemHealth) => {
  const activities = []
  let id = 1

  // Add user activity
  if (stats.overview.activeUsers > 0) {
    activities.push({
      id: id++,
      type: 'user_login',
      title: 'Active Users',
      description: `${stats.overview.activeUsers} users currently active`,
      timestamp: new Date(),
      severity: 'info'
    })
  }

  // Add system health activity
  activities.push({
    id: id++,
    type: 'system_update',
    title: 'System Health',
    description: `System status: ${health.health.status.toUpperCase()}`,
    timestamp: new Date(Date.now() - 60000),
    severity: health.health.status === 'healthy' ? 'success' : 'warning'
  })

  // Add error activity if there are recent errors
  if (stats.overview.recentErrors > 0) {
    activities.push({
      id: id++,
      type: 'security_alert',
      title: 'Recent Errors',
      description: `${stats.overview.recentErrors} errors in the last 24 hours`,
      timestamp: new Date(Date.now() - 300000),
      severity: 'warning'
    })
  }

  realtimeActivities.value = activities
}

const generateSystemAlerts = (health: SystemHealth, stats: DashboardStats) => {
  const alerts = []
  let id = 1

  // Memory usage alert
  if (systemMetrics.memoryUsage > 80) {
    alerts.push({
      id: id++,
      level: 'warning',
      title: 'High Memory Usage',
      message: `Memory usage is at ${systemMetrics.memoryUsage}%. Consider optimizing or scaling.`,
      timestamp: new Date(Date.now() - 900000)
    })
  }

  // Error rate alert
  if (stats.overview.recentErrors > 10) {
    alerts.push({
      id: id++,
      level: 'error',
      title: 'High Error Rate',
      message: `${stats.overview.recentErrors} errors detected in the last 24 hours.`,
      timestamp: new Date(Date.now() - 600000)
    })
  }

  // Low activity alert
  if (stats.overview.activeUsers < 5) {
    alerts.push({
      id: id++,
      level: 'info',
      title: 'Low User Activity',
      message: 'User activity is lower than usual. This might be normal for off-peak hours.',
      timestamp: new Date(Date.now() - 1800000)
    })
  }

  systemAlerts.value = alerts
}

const updateMetrics = async () => {
  // Refresh data periodically
  await fetchDashboardData()
}

// Lifecycle
onMounted(async () => {
  // Initial data fetch
  await fetchDashboardData()

  // Start real-time updates with longer interval for better performance
  updateInterval = setInterval(updateMetrics, 30000) // Every 30 seconds for real API calls
})

onUnmounted(() => {
  if (updateInterval) {
    clearInterval(updateInterval)
    updateInterval = null
  }
})
</script>

<style scoped>
.card-header {
  @apply flex items-center justify-between p-6 border-b border-base-200;
}

.card-title {
  @apply text-lg font-semibold;
}

/* Custom scrollbar */
.overflow-y-auto::-webkit-scrollbar {
  width: 4px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: transparent;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: hsl(var(--bc) / 0.2);
  border-radius: 2px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--bc) / 0.3);
}
</style>
