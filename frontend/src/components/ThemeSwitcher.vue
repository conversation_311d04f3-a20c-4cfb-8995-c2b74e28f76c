<template>
  <div class="theme-switcher">
    <!-- Simple Theme Toggle Button -->
    <button
      @click="toggleTheme"
      class="btn btn-ghost btn-circle"
      :title="themeTooltip"
    >
      <Icon
        :name="currentThemeIcon"
        size="lg"
        class="transition-all duration-300"
        :class="{ 'rotate-180': isTransitioning }"
      />
    </button>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import Icon from '@/components/common/Icon.vue'

// Theme state - simplified to just 3 states: auto, light, dark
const currentTheme = ref<'auto' | 'light' | 'dark'>('auto')
const systemPrefersDark = ref(false)
const isTransitioning = ref(false)

// Computed
const currentThemeIcon = computed(() => {
  if (currentTheme.value === 'auto') {
    return systemPrefersDark.value ? 'moon' : 'sun'
  }
  return currentTheme.value === 'dark' ? 'moon' : 'sun'
})

const themeTooltip = computed(() => {
  switch (currentTheme.value) {
    case 'auto':
      return `Auto (${systemPrefersDark.value ? 'Dark' : 'Light'})`
    case 'light':
      return 'Light Theme'
    case 'dark':
      return 'Dark Theme'
    default:
      return 'Toggle Theme'
  }
})

const effectiveTheme = computed(() => {
  if (currentTheme.value === 'auto') {
    return systemPrefersDark.value ? 'hlenergy-dark' : 'hlenergy-light'
  }
  return currentTheme.value === 'dark' ? 'hlenergy-dark' : 'hlenergy-light'
})

// Methods
const toggleTheme = () => {
  isTransitioning.value = true

  // Cycle through: auto -> light -> dark -> auto
  switch (currentTheme.value) {
    case 'auto':
      currentTheme.value = 'light'
      break
    case 'light':
      currentTheme.value = 'dark'
      break
    case 'dark':
      currentTheme.value = 'auto'
      break
  }

  applyTheme()
  saveThemePreference()

  // Reset transition state
  setTimeout(() => {
    isTransitioning.value = false
  }, 300)
}

const applyTheme = () => {
  const theme = effectiveTheme.value
  document.documentElement.setAttribute('data-theme', theme)

  // Add smooth transition class
  document.documentElement.classList.add('theme-transition')

  // Remove transition class after animation
  setTimeout(() => {
    document.documentElement.classList.remove('theme-transition')
  }, 300)

  console.log(`Applied theme: ${theme} (mode: ${currentTheme.value})`)
}

const saveThemePreference = () => {
  localStorage.setItem('hlenergy-theme', currentTheme.value)
}

const loadThemePreference = () => {
  const saved = localStorage.getItem('hlenergy-theme') as 'auto' | 'light' | 'dark'
  if (saved && ['auto', 'light', 'dark'].includes(saved)) {
    currentTheme.value = saved
  } else {
    // Default to auto theme (system preference)
    currentTheme.value = 'auto'
  }
}

const checkSystemTheme = () => {
  if (window.matchMedia) {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
    systemPrefersDark.value = mediaQuery.matches

    // Listen for changes
    mediaQuery.addEventListener('change', (e) => {
      systemPrefersDark.value = e.matches
      if (currentTheme.value === 'auto') {
        applyTheme()
      }
    })
  }
}

const initializeTheme = () => {
  checkSystemTheme()
  loadThemePreference()
  applyTheme()
}

// Lifecycle
onMounted(() => {
  initializeTheme()
})

// Expose theme functions for external use
defineExpose({
  toggleTheme,
  currentTheme,
  effectiveTheme
})
</script>

<style scoped>
.theme-switcher {
  position: relative;
}

/* Theme transition animation */
:global(html.theme-transition) {
  transition: background-color 0.3s ease-in-out, color 0.3s ease-in-out;
}

:global(html.theme-transition *) {
  transition: background-color 0.3s ease-in-out, color 0.3s ease-in-out, border-color 0.3s ease-in-out;
}

/* Button hover effect */
.btn:hover {
  transform: scale(1.05);
}

/* Icon rotation animation */
.rotate-180 {
  transform: rotate(180deg);
}

/* Smooth transitions */
.transition-all {
  transition: all 0.3s ease-in-out;
}
</style>
