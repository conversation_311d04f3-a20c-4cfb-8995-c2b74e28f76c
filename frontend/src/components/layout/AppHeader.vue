<template>
  <header
    class="navbar bg-base-100 shadow-lg px-2 sm:px-4 sticky top-0 z-50 transition-transform duration-300 ease-in-out"
    :class="{ '-translate-y-full': isHidden }"
  >
    <div class="navbar-start">
      <!-- Mobile Menu -->
      <div class="dropdown">
        <div tabindex="0" role="button" class="btn btn-ghost btn-square lg:hidden hover:bg-base-200">
          <Icon name="menu" size="md" />
        </div>
        <ul
          tabindex="0"
          class="menu menu-sm dropdown-content bg-base-100 rounded-box z-[1] mt-3 w-72 p-4 shadow-xl border border-base-300"
        >
          <li class="menu-title mb-2">
            <span class="text-base-content opacity-70 font-semibold">Navigation</span>
          </li>
          <li><RouterLink to="/" class="flex items-center gap-3 py-3 px-4 rounded-lg hover:bg-base-200 transition-colors">
            <Icon name="home" size="sm" class="flex-shrink-0" />
            <span class="truncate">{{ $t('navigation.home') }}</span>
          </RouterLink></li>
          <li><RouterLink to="/about" class="flex items-center gap-3 py-3 px-4 rounded-lg hover:bg-base-200 transition-colors">
            <Icon name="user" size="sm" class="flex-shrink-0" />
            <span class="truncate">{{ $t('navigation.about') }}</span>
          </RouterLink></li>
          <li><RouterLink to="/services" class="flex items-center gap-3 py-3 px-4 rounded-lg hover:bg-base-200 transition-colors">
            <Icon name="lightbulb" size="sm" class="flex-shrink-0" />
            <span class="truncate">{{ $t('navigation.services') }}</span>
          </RouterLink></li>
          <li><RouterLink to="/contact" class="flex items-center gap-3 py-3 px-4 rounded-lg hover:bg-base-200 transition-colors">
            <Icon name="email" size="sm" class="flex-shrink-0" />
            <span class="truncate">{{ $t('navigation.contact') }}</span>
          </RouterLink></li>
          <li><RouterLink to="/theme-demo" class="flex items-center gap-3 py-3 px-4 rounded-lg hover:bg-base-200 transition-colors">
            <Icon name="sparkles" size="sm" />
            Theme Demo
          </RouterLink></li>

          <!-- Authenticated Mobile Menu Items -->
          <template v-if="authStore.isAuthenticated">
            <li class="menu-title mt-4 mb-2">
              <span class="text-base-content opacity-70 font-semibold">Account</span>
            </li>
            <li><RouterLink to="/dashboard" class="flex items-center gap-3 py-3 px-4 rounded-lg hover:bg-base-200 transition-colors">
              <Icon name="chart-bar" size="sm" class="flex-shrink-0" />
              <span class="truncate">{{ $t('navigation.dashboard') }}</span>
            </RouterLink></li>
            <li><RouterLink to="/profile" class="flex items-center gap-3 py-3 px-4 rounded-lg hover:bg-base-200 transition-colors">
              <Icon name="user" size="sm" class="flex-shrink-0" />
              <span class="truncate">{{ $t('navigation.profile') }}</span>
            </RouterLink></li>
            <li v-if="authStore.isAdmin"><RouterLink to="/admin" class="flex items-center gap-3 py-3 px-4 rounded-lg hover:bg-base-200 transition-colors">
              <Icon name="cog" size="sm" class="flex-shrink-0" />
              <span class="truncate">Admin Panel</span>
            </RouterLink></li>
            <li><RouterLink to="/crm" class="flex items-center gap-3 py-3 px-4 rounded-lg hover:bg-base-200 transition-colors">
              <Icon name="users" size="sm" class="flex-shrink-0" />
              <span class="truncate">CRM</span>
            </RouterLink></li>
          </template>

          <!-- Offline Status -->
          <li v-if="!isOnline" class="mt-4">
            <span class="flex items-center gap-3 py-3 px-4 text-warning bg-warning/10 rounded-lg">
              <Icon name="wifi" size="sm" />
              <span class="badge badge-warning badge-sm">Offline</span>
            </span>
          </li>

          <!-- Mobile Auth Actions -->
          <template v-if="!authStore.isAuthenticated">
            <li class="menu-title mt-4 mb-2">
              <span class="text-base-content opacity-70 font-semibold">Account</span>
            </li>
            <li><RouterLink to="/login" class="flex items-center gap-3 py-3 px-4 rounded-lg hover:bg-base-200 transition-colors">
              <Icon name="login" size="sm" class="flex-shrink-0" />
              <span class="truncate">{{ $t('auth.login') }}</span>
            </RouterLink></li>
            <li><RouterLink to="/register" class="flex items-center gap-3 py-3 px-4 rounded-lg bg-primary text-primary-content hover:bg-primary-focus transition-colors">
              <Icon name="user-plus" size="sm" class="flex-shrink-0" />
              <span class="truncate">{{ $t('auth.register') }}</span>
            </RouterLink></li>
          </template>

          <!-- Mobile Logout -->
          <template v-else>
            <li class="mt-4">
              <a @click="handleLogout" class="flex items-center gap-3 py-3 px-4 rounded-lg text-error hover:bg-error/10 transition-colors">
                <Icon name="logout" size="sm" class="flex-shrink-0" />
                <span class="truncate">{{ $t('auth.logout') }}</span>
              </a>
            </li>
          </template>
        </ul>
      </div>
      <RouterLink to="/" class="btn btn-ghost hover:bg-base-200 px-2 sm:px-4">
        <Logo size="sm" :show-text="true" class="hover:scale-105 transition-transform duration-200" />
      </RouterLink>
    </div>
    
    <div class="navbar-center hidden lg:flex">
      <ul class="menu menu-horizontal px-1">
        <li><RouterLink to="/">Home</RouterLink></li>
        <li><RouterLink to="/about">About</RouterLink></li>
        <li><RouterLink to="/services">Services</RouterLink></li>
        <li><RouterLink to="/contact">Contact</RouterLink></li>
        <li><RouterLink to="/theme-demo">Themes</RouterLink></li>
        <li v-if="!isOnline">
          <span class="flex items-center gap-2 text-warning">
            <Icon name="wifi" size="xs" />
            <span class="badge badge-warning badge-xs">Offline</span>
          </span>
        </li>
      </ul>
    </div>
    
    <div class="navbar-end gap-1 sm:gap-2">
      <!-- Socket.io Status Indicator -->
      <div class="hidden sm:block">
        <SocketStatus />
      </div>

      <!-- Theme Switcher -->
      <ThemeSwitcher />

      <!-- Language Selector -->
      <div class="dropdown dropdown-end">
        <div tabindex="0" role="button" class="btn btn-ghost btn-sm sm:btn-md gap-1 sm:gap-2 hover:bg-base-200">
          <Icon name="globe" size="sm" />
          <span class="hidden sm:inline text-sm">{{ currentLocale.toUpperCase() }}</span>
          <Icon name="chevron-down" size="xs" />
        </div>
        <ul tabindex="0" class="dropdown-content menu bg-base-100 rounded-box z-[1] w-36 p-2 shadow-xl border border-base-300">
          <li><a @click="changeLocale('en')" class="flex items-center gap-2 py-2 hover:bg-base-200 rounded-lg transition-colors">
            <span class="text-sm font-medium">🇺🇸</span>
            English
          </a></li>
          <li><a @click="changeLocale('es')" class="flex items-center gap-2 py-2 hover:bg-base-200 rounded-lg transition-colors">
            <span class="text-sm font-medium">🇪🇸</span>
            Español
          </a></li>
          <li><a @click="changeLocale('pt')" class="flex items-center gap-2 py-2 hover:bg-base-200 rounded-lg transition-colors">
            <span class="text-sm font-medium">🇵🇹</span>
            Português
          </a></li>
        </ul>
      </div>
      
      <!-- Authentication Section -->
      <div class="hidden lg:flex items-center gap-2 ml-4">
        <!-- Unauthenticated State -->
        <template v-if="!authStore.isAuthenticated">
          <RouterLink to="/login" class="btn btn-ghost btn-sm sm:btn-md hover:bg-base-200">
            <span class="hidden sm:inline">{{ $t('auth.login') }}</span>
            <span class="sm:hidden">{{ $t('auth.login').substring(0, 4) }}</span>
          </RouterLink>
          <RouterLink to="/register" class="btn btn-primary btn-sm sm:btn-md hover:bg-primary-focus">
            <span class="hidden sm:inline">{{ $t('auth.register') }}</span>
            <span class="sm:hidden">{{ $t('auth.register').substring(0, 4) }}</span>
          </RouterLink>
        </template>

        <!-- Authenticated State -->
        <template v-else>
          <!-- User Menu Dropdown -->
          <div class="dropdown dropdown-end">
            <div tabindex="0" role="button" class="btn btn-ghost btn-circle avatar hover:bg-base-200">
              <div class="w-10 rounded-full bg-primary text-primary-content flex items-center justify-center font-semibold">
                {{ authStore.userInitials }}
              </div>
            </div>
            <ul tabindex="0" class="dropdown-content menu bg-base-100 rounded-box z-[1] w-56 p-3 shadow-xl border border-base-300">
              <li class="menu-title mb-2">
                <span class="text-sm font-semibold">{{ authStore.user?.name }}</span>
                <span class="text-xs opacity-70">{{ authStore.user?.email }}</span>
              </li>
              <li><RouterLink to="/dashboard" class="py-2 hover:bg-base-200 rounded-lg transition-colors">
                <Icon name="chart-bar" size="sm" />
                Dashboard
              </RouterLink></li>
              <li><RouterLink to="/profile" class="py-2 hover:bg-base-200 rounded-lg transition-colors">
                <Icon name="user" size="sm" />
                Profile
              </RouterLink></li>
              <li v-if="authStore.isAdmin"><RouterLink to="/admin" class="py-2 hover:bg-base-200 rounded-lg transition-colors">
                <Icon name="cog" size="sm" />
                Admin Panel
              </RouterLink></li>
              <li><RouterLink to="/crm" class="py-2 hover:bg-base-200 rounded-lg transition-colors">
                <Icon name="users" size="sm" />
                CRM
              </RouterLink></li>
              <li><hr class="my-2"></li>
              <li><a @click="handleLogout" class="py-2 text-error hover:bg-error/10 rounded-lg transition-colors">
                <Icon name="logout" size="sm" />
                Logout
              </a></li>
            </ul>
          </div>
        </template>
      </div>
    </div>
  </header>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, onUnmounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { usePWA } from '@/composables/usePWA'
import Icon from '@/components/common/Icon.vue'
import Logo from '@/components/common/Logo.vue'
import ThemeSwitcher from '@/components/ThemeSwitcher.vue'
import SocketStatus from '@/components/common/SocketStatus.vue'

const { locale, t } = useI18n()
const router = useRouter()
const authStore = useAuthStore()
const { isOnline } = usePWA()

const currentLocale = computed(() => locale.value)

// Navbar scroll behavior
const isHidden = ref(false)
const lastScrollY = ref(0)
const scrollThreshold = 10 // Minimum scroll distance to trigger hide/show

const handleScroll = () => {
  const currentScrollY = window.scrollY

  // Don't hide navbar when at the top of the page
  if (currentScrollY < scrollThreshold) {
    isHidden.value = false
    lastScrollY.value = currentScrollY
    return
  }

  // Hide navbar when scrolling down, show when scrolling up
  if (currentScrollY > lastScrollY.value && currentScrollY > scrollThreshold) {
    isHidden.value = true
  } else if (currentScrollY < lastScrollY.value) {
    isHidden.value = false
  }

  lastScrollY.value = currentScrollY
}

const changeLocale = (newLocale: string) => {
  locale.value = newLocale
  localStorage.setItem('locale', newLocale)
}

const handleLogout = async () => {
  try {
    await authStore.logout()
    router.push('/')
    // Show success message (you can add a toast notification here)
  } catch (error) {
    console.error('Logout error:', error)
  }
}

// Load saved locale on component mount
const savedLocale = localStorage.getItem('locale')
if (savedLocale) {
  locale.value = savedLocale
}

// Setup scroll listener
onMounted(() => {
  window.addEventListener('scroll', handleScroll, { passive: true })
})

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
})
</script>

<style scoped>
.router-link-active {
  font-weight: 600;
  color: hsl(var(--p));
}
</style>
