<template>
  <div v-if="showInstallPrompt" class="fixed bottom-4 left-4 right-4 z-50 md:left-auto md:right-4 md:w-96">
    <div class="alert alert-info shadow-lg">
      <div class="flex-1">
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="w-6 h-6 mx-2 stroke-current">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <div>
          <h3 class="font-bold">Install HLenergy App</h3>
          <div class="text-xs">Get the full app experience with offline access</div>
        </div>
      </div>
      <div class="flex-none">
        <button @click="handleInstall" class="btn btn-sm btn-primary">
          Install
        </button>
        <button @click="handleDismiss" class="btn btn-sm btn-ghost ml-2">
          ✕
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { usePWA } from '@/composables/usePWA'

const { showInstallPrompt, handleInstallPrompt, dismissInstallPrompt } = usePWA()

const handleInstall = async () => {
  const installed = await handleInstallPrompt()
  if (installed) {
    // Show success message or redirect
    console.log('App installed successfully!')
  }
}

const handleDismiss = () => {
  dismissInstallPrompt()
}
</script>
