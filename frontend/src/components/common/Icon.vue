<template>
  <component 
    :is="iconComponent" 
    :class="iconClasses"
    v-bind="$attrs"
  />
</template>

<script setup lang="ts">
import { computed } from 'vue'
import {
  // Outline icons
  HomeIcon,
  UserIcon,
  CogIcon,
  BellIcon,
  EnvelopeIcon,
  PhoneIcon,
  CalendarIcon,
  ClockIcon,
  DocumentIcon,
  FolderIcon,
  ChartBarIcon,
  CurrencyDollarIcon,
  BuildingOfficeIcon,
  UsersIcon,
  GlobeAltIcon,
  LightBulbIcon,
  BoltIcon,
  FireIcon,
  SunIcon,
  MoonIcon,
  StarIcon,
  HeartIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  PlusIcon,
  MinusIcon,
  XMarkIcon,
  CheckIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  CheckCircleIcon,
  XCircleIcon,
  ArrowRightIcon,
  ArrowLeftIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  ChevronRightIcon,
  ChevronLeftIcon,
  ChevronUpIcon,
  ChevronDownIcon,
  MagnifyingGlassIcon,
  AdjustmentsHorizontalIcon,
  Bars3Icon,
  EllipsisVerticalIcon,
  ShareIcon,
  LinkIcon,
  CloudIcon,
  ServerIcon,
  ComputerDesktopIcon,
  DevicePhoneMobileIcon,
  WifiIcon,
  SignalIcon,
  BanknotesIcon,
  CreditCardIcon,
  ShoppingCartIcon,
  TruckIcon,
  MapPinIcon,
  GiftIcon,
  TagIcon,
  BookmarkIcon,
  ChatBubbleLeftIcon,
  VideoCameraIcon,
  MicrophoneIcon,
  SpeakerWaveIcon,
  MusicalNoteIcon,
  PhotoIcon,
  FilmIcon,
  DocumentTextIcon,
  ArchiveBoxIcon,
  ClipboardIcon,
  PrinterIcon,
  QrCodeIcon,
  KeyIcon,
  LockClosedIcon,
  LockOpenIcon,
  ShieldCheckIcon,
  EyeSlashIcon,
  FingerPrintIcon,
  UserGroupIcon,
  AcademicCapIcon,
  BeakerIcon,
  CalculatorIcon,
  ClipboardDocumentListIcon,
  PresentationChartBarIcon,
  TableCellsIcon,
  ChartPieIcon,
  TrophyIcon,
  RocketLaunchIcon,
  SparklesIcon,
  HandThumbUpIcon,
  HandThumbDownIcon,
  FaceSmileIcon,
  FaceFrownIcon,
  ChatBubbleOvalLeftEllipsisIcon,
  ExclamationCircleIcon,
  QuestionMarkCircleIcon,
  LightBulbIcon as LightBulbOutlineIcon,
  BoltIcon as BoltOutlineIcon,
  CircleStackIcon,
  ArrowDownTrayIcon,
  ArrowPathIcon,

  UserPlusIcon,
  PlayIcon,
  PauseIcon,
  StopIcon,
  ForwardIcon,
  BackwardIcon,
  SpeakerXMarkIcon,
  WrenchScrewdriverIcon,
  CommandLineIcon,
  TableCellsIcon as TableIcon,
  FunnelIcon,
  DocumentDuplicateIcon,
  ClipboardDocumentCheckIcon,
  BookOpenIcon,
  NewspaperIcon,
  MegaphoneIcon,
  BugAntIcon,
  CpuChipIcon,
  ArrowTrendingUpIcon,
  HashtagIcon,
} from '@heroicons/vue/24/outline'

import {
  // Solid icons for filled variants
  HomeIcon as HomeSolid,
  UserIcon as UserSolid,
  CogIcon as CogSolid,
  BellIcon as BellSolid,
  EnvelopeIcon as EnvelopeSolid,
  PhoneIcon as PhoneSolid,
  CalendarIcon as CalendarSolid,
  ClockIcon as ClockSolid,
  DocumentIcon as DocumentSolid,
  FolderIcon as FolderSolid,
  ChartBarIcon as ChartBarSolid,
  CurrencyDollarIcon as CurrencyDollarSolid,
  BuildingOfficeIcon as BuildingOfficeSolid,
  UsersIcon as UsersSolid,
  StarIcon as StarSolid,
  HeartIcon as HeartSolid,
  EyeIcon as EyeSolid,
  CheckIcon as CheckSolid,
  ExclamationTriangleIcon as ExclamationTriangleSolid,
  InformationCircleIcon as InformationCircleSolid,
  CheckCircleIcon as CheckCircleSolid,
  XCircleIcon as XCircleSolid,
  LightBulbIcon as LightBulbSolid,
  BoltIcon as BoltSolid,
  SunIcon as SunSolid,
  MoonIcon as MoonSolid,
  FireIcon as FireSolid,
  SparklesIcon as SparklesSolid,
  TrophyIcon as TrophySolid,
  RocketLaunchIcon as RocketLaunchSolid,
  HandThumbUpIcon as HandThumbUpSolid,
  HandThumbDownIcon as HandThumbDownSolid,
  FaceSmileIcon as FaceSmileSolid,
  FaceFrownIcon as FaceFrownSolid,
  ShieldCheckIcon as ShieldCheckSolid,
  LockClosedIcon as LockClosedSolid,
  KeyIcon as KeySolid,
  CircleStackIcon as CircleStackSolid,
  ArrowDownTrayIcon as ArrowDownTraySolid,
  ArrowPathIcon as ArrowPathSolid,

  UserPlusIcon as UserPlusSolid,
  PlayIcon as PlaySolid,
  PauseIcon as PauseSolid,
} from '@heroicons/vue/24/solid'

interface Props {
  name: string
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl'
  variant?: 'outline' | 'solid'
  class?: string
}

const props = withDefaults(defineProps<Props>(), {
  size: 'md',
  variant: 'outline',
  class: ''
})

// Icon mapping
const iconMap = {
  // Navigation & UI
  home: { outline: HomeIcon, solid: HomeSolid },
  user: { outline: UserIcon, solid: UserSolid },
  users: { outline: UsersIcon, solid: UsersSolid },
  settings: { outline: CogIcon, solid: CogSolid },
  bell: { outline: BellIcon, solid: BellSolid },
  menu: { outline: Bars3Icon, solid: Bars3Icon },
  search: { outline: MagnifyingGlassIcon, solid: MagnifyingGlassIcon },
  filter: { outline: AdjustmentsHorizontalIcon, solid: AdjustmentsHorizontalIcon },
  more: { outline: EllipsisVerticalIcon, solid: EllipsisVerticalIcon },
  
  // Communication
  email: { outline: EnvelopeIcon, solid: EnvelopeSolid },
  mail: { outline: EnvelopeIcon, solid: EnvelopeSolid }, // Alias for email
  phone: { outline: PhoneIcon, solid: PhoneSolid },
  chat: { outline: ChatBubbleLeftIcon, solid: ChatBubbleLeftIcon },
  quote: { outline: ChatBubbleOvalLeftEllipsisIcon, solid: ChatBubbleOvalLeftEllipsisIcon },
  video: { outline: VideoCameraIcon, solid: VideoCameraIcon },
  microphone: { outline: MicrophoneIcon, solid: MicrophoneIcon },
  speaker: { outline: SpeakerWaveIcon, solid: SpeakerWaveIcon },
  
  // Time & Calendar
  calendar: { outline: CalendarIcon, solid: CalendarSolid },
  clock: { outline: ClockIcon, solid: ClockSolid },
  
  // Documents & Files
  document: { outline: DocumentIcon, solid: DocumentSolid },
  'document-text': { outline: DocumentTextIcon, solid: DocumentTextIcon },
  folder: { outline: FolderIcon, solid: FolderSolid },
  archive: { outline: ArchiveBoxIcon, solid: ArchiveBoxIcon },
  clipboard: { outline: ClipboardIcon, solid: ClipboardIcon },
  'clipboard-list': { outline: ClipboardDocumentListIcon, solid: ClipboardDocumentListIcon },
  
  // Charts & Analytics
  'chart-bar': { outline: ChartBarIcon, solid: ChartBarSolid },
  'chart-pie': { outline: ChartPieIcon, solid: ChartPieIcon },
  'presentation-chart': { outline: PresentationChartBarIcon, solid: PresentationChartBarIcon },
  'trending-up': { outline: ArrowTrendingUpIcon, solid: ArrowTrendingUpIcon },
  table: { outline: TableCellsIcon, solid: TableCellsIcon },
  
  // Business & Finance
  currency: { outline: CurrencyDollarIcon, solid: CurrencyDollarSolid },
  banknotes: { outline: BanknotesIcon, solid: BanknotesIcon },
  'credit-card': { outline: CreditCardIcon, solid: CreditCardIcon },
  building: { outline: BuildingOfficeIcon, solid: BuildingOfficeSolid },
  'shopping-cart': { outline: ShoppingCartIcon, solid: ShoppingCartIcon },
  truck: { outline: TruckIcon, solid: TruckIcon },
  
  // Energy & Environment
  lightbulb: { outline: LightBulbIcon, solid: LightBulbSolid },
  bolt: { outline: BoltIcon, solid: BoltSolid },
  fire: { outline: FireIcon, solid: FireSolid },
  sun: { outline: SunIcon, solid: SunSolid },
  moon: { outline: MoonIcon, solid: MoonSolid },
  globe: { outline: GlobeAltIcon, solid: GlobeAltIcon },
  
  // Actions
  plus: { outline: PlusIcon, solid: PlusIcon },
  minus: { outline: MinusIcon, solid: MinusIcon },
  edit: { outline: PencilIcon, solid: PencilIcon },
  trash: { outline: TrashIcon, solid: TrashIcon },
  close: { outline: XMarkIcon, solid: XMarkIcon },
  check: { outline: CheckIcon, solid: CheckSolid },
  
  // Arrows & Navigation
  'arrow-right': { outline: ArrowRightIcon, solid: ArrowRightIcon },
  'arrow-left': { outline: ArrowLeftIcon, solid: ArrowLeftIcon },
  'arrow-up': { outline: ArrowUpIcon, solid: ArrowUpIcon },
  'arrow-down': { outline: ArrowDownIcon, solid: ArrowDownIcon },
  'chevron-right': { outline: ChevronRightIcon, solid: ChevronRightIcon },
  'chevron-left': { outline: ChevronLeftIcon, solid: ChevronLeftIcon },
  'chevron-up': { outline: ChevronUpIcon, solid: ChevronUpIcon },
  'chevron-down': { outline: ChevronDownIcon, solid: ChevronDownIcon },
  
  // Status & Feedback
  warning: { outline: ExclamationTriangleIcon, solid: ExclamationTriangleSolid },
  'exclamation-triangle': { outline: ExclamationTriangleIcon, solid: ExclamationTriangleSolid },
  info: { outline: InformationCircleIcon, solid: InformationCircleSolid },
  success: { outline: CheckCircleIcon, solid: CheckCircleSolid },
  'check-circle': { outline: CheckCircleIcon, solid: CheckCircleSolid },
  error: { outline: XCircleIcon, solid: XCircleSolid },
  question: { outline: QuestionMarkCircleIcon, solid: QuestionMarkCircleIcon },
  exclamation: { outline: ExclamationCircleIcon, solid: ExclamationCircleIcon },
  
  // Social & Engagement
  star: { outline: StarIcon, solid: StarSolid },
  heart: { outline: HeartIcon, solid: HeartSolid },
  'thumb-up': { outline: HandThumbUpIcon, solid: HandThumbUpSolid },
  'thumb-down': { outline: HandThumbDownIcon, solid: HandThumbDownSolid },
  'face-smile': { outline: FaceSmileIcon, solid: FaceSmileSolid },
  'face-frown': { outline: FaceFrownIcon, solid: FaceFrownSolid },
  
  // Security
  lock: { outline: LockClosedIcon, solid: LockClosedSolid },
  'lock-open': { outline: LockOpenIcon, solid: LockOpenIcon },
  unlock: { outline: LockOpenIcon, solid: LockOpenIcon }, // Alias for lock-open
  key: { outline: KeyIcon, solid: KeySolid },
  shield: { outline: ShieldCheckIcon, solid: ShieldCheckSolid },
  'shield-check': { outline: ShieldCheckIcon, solid: ShieldCheckSolid }, // Alias for shield
  eye: { outline: EyeIcon, solid: EyeSolid },
  'eye-slash': { outline: EyeSlashIcon, solid: EyeSlashIcon },
  fingerprint: { outline: FingerPrintIcon, solid: FingerPrintIcon },
  
  // Technology
  computer: { outline: ComputerDesktopIcon, solid: ComputerDesktopIcon },
  mobile: { outline: DevicePhoneMobileIcon, solid: DevicePhoneMobileIcon },
  wifi: { outline: WifiIcon, solid: WifiIcon },
  signal: { outline: SignalIcon, solid: SignalIcon },
  server: { outline: ServerIcon, solid: ServerIcon },
  cloud: { outline: CloudIcon, solid: CloudIcon },
  qr: { outline: QrCodeIcon, solid: QrCodeIcon },
  database: { outline: CircleStackIcon, solid: CircleStackSolid },
  'cpu-chip': { outline: CpuChipIcon, solid: CpuChipIcon },

  // File Operations
  download: { outline: ArrowDownTrayIcon, solid: ArrowDownTraySolid },
  upload: { outline: ArrowDownTrayIcon, solid: ArrowDownTraySolid }, // Using same icon, rotated
  copy: { outline: DocumentDuplicateIcon, solid: DocumentDuplicateIcon },

  // System Operations
  refresh: { outline: ArrowPathIcon, solid: ArrowPathSolid },
  logout: { outline: ArrowRightIcon, solid: ArrowRightIcon },
  'user-plus': { outline: UserPlusIcon, solid: UserPlusSolid },

  // Media Controls
  play: { outline: PlayIcon, solid: PlaySolid },
  pause: { outline: PauseIcon, solid: PauseSolid },
  stop: { outline: StopIcon, solid: StopIcon },
  forward: { outline: ForwardIcon, solid: ForwardIcon },
  backward: { outline: BackwardIcon, solid: BackwardIcon },

  // Development & Tools
  terminal: { outline: CommandLineIcon, solid: CommandLineIcon },
  wrench: { outline: WrenchScrewdriverIcon, solid: WrenchScrewdriverIcon },
  bug: { outline: BugAntIcon, solid: BugAntIcon },
  chip: { outline: CpuChipIcon, solid: CpuChipIcon },

  // Additional Aliases
  cog: { outline: CogIcon, solid: CogSolid }, // Alias for settings
  x: { outline: XMarkIcon, solid: XMarkIcon }, // Alias for close
  login: { outline: ArrowRightIcon, solid: ArrowRightIcon }, // Alias for logout (same icon)
  'log-in': { outline: ArrowRightIcon, solid: ArrowRightIcon }, // Alias for login
  'log-out': { outline: ArrowRightIcon, solid: ArrowRightIcon }, // Alias for logout
  'wifi-off': { outline: WifiIcon, solid: WifiIcon }, // Alias for wifi (could be styled differently)
  palette: { outline: SparklesIcon, solid: SparklesSolid }, // Alias for theme/color selection
  hashtag: { outline: HashtagIcon, solid: HashtagIcon }, // Hashtag symbol

  // Socket.io and Analytics Icons
  zap: { outline: BoltIcon, solid: BoltSolid }, // Alias for bolt/lightning
  'chart-line': { outline: ChartBarIcon, solid: ChartBarSolid }, // Alias for chart-bar
  'cursor-click': { outline: HandThumbUpIcon, solid: HandThumbUpSolid }, // Click representation
  form: { outline: DocumentTextIcon, solid: DocumentTextIcon }, // Form representation
  'message-circle': { outline: ChatBubbleLeftIcon, solid: ChatBubbleLeftIcon }, // Message alias
  send: { outline: ArrowRightIcon, solid: ArrowRightIcon }, // Send action
  'alert-triangle': { outline: ExclamationTriangleIcon, solid: ExclamationTriangleSolid }, // Alert alias
  activity: { outline: ChartBarIcon, solid: ChartBarSolid }, // Activity representation
  broadcast: { outline: MegaphoneIcon, solid: MegaphoneIcon }, // Broadcast representation
  list: { outline: TableCellsIcon, solid: TableCellsIcon }, // List representation
  smartphone: { outline: DevicePhoneMobileIcon, solid: DevicePhoneMobileIcon }, // Mobile device alias
  
  // Achievements & Gamification
  trophy: { outline: TrophyIcon, solid: TrophySolid },
  rocket: { outline: RocketLaunchIcon, solid: RocketLaunchSolid },
  sparkles: { outline: SparklesIcon, solid: SparklesSolid },
  academic: { outline: AcademicCapIcon, solid: AcademicCapIcon },
  beaker: { outline: BeakerIcon, solid: BeakerIcon },
  calculator: { outline: CalculatorIcon, solid: CalculatorIcon },
}

const iconComponent = computed(() => {
  const icon = iconMap[props.name as keyof typeof iconMap]
  if (!icon) {
    console.warn(`Icon "${props.name}" not found`)
    return QuestionMarkCircleIcon
  }
  return icon[props.variant]
})

const sizeClasses = {
  xs: 'w-3 h-3',
  sm: 'w-4 h-4', 
  md: 'w-5 h-5',
  lg: 'w-6 h-6',
  xl: 'w-8 h-8',
  '2xl': 'w-10 h-10'
}

const iconClasses = computed(() => {
  return [
    sizeClasses[props.size],
    props.class
  ].filter(Boolean).join(' ')
})
</script>
