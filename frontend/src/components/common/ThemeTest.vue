<template>
  <div class="p-6 space-y-6">
    <div class="text-center">
      <h2 class="text-3xl font-bold text-base-content mb-2">DaisyUI Theme Test</h2>
      <p class="text-base-content opacity-70">Testing all available themes</p>
    </div>

    <!-- Current Theme Display -->
    <div class="alert alert-info">
      <Icon name="info" size="sm" />
      <span>Current theme: <strong>{{ currentTheme }}</strong></span>
    </div>

    <!-- Theme Debug Info -->
    <div class="alert alert-warning">
      <Icon name="warning" size="sm" />
      <div>
        <div><strong>HTML data-theme:</strong> {{ htmlTheme }}</div>
        <div><strong>Primary color:</strong> <span class="text-primary">{{ primaryColor }}</span></div>
        <div><strong>Secondary color:</strong> <span class="text-secondary">{{ secondaryColor }}</span></div>
        <button @click="debugTheme" class="btn btn-xs btn-warning mt-2">Debug Theme</button>
      </div>
    </div>

    <!-- Theme Grid -->
    <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
      <button
        v-for="theme in testThemes"
        :key="theme"
        @click="setTheme(theme)"
        :class="{ 'ring-2 ring-primary': currentTheme === theme }"
        class="btn btn-outline btn-sm h-auto p-3 flex flex-col items-center gap-2 hover:scale-105 transition-transform"
      >
        <span class="text-xs font-medium">{{ theme }}</span>
        <div class="flex gap-1">
          <div class="w-3 h-3 rounded-full bg-primary"></div>
          <div class="w-3 h-3 rounded-full bg-secondary"></div>
          <div class="w-3 h-3 rounded-full bg-accent"></div>
        </div>
      </button>
    </div>

    <!-- Theme Preview Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <!-- Primary Colors -->
      <div class="card bg-base-100 shadow-lg">
        <div class="card-body">
          <h3 class="card-title text-primary">Primary Colors</h3>
          <div class="space-y-2">
            <button class="btn btn-primary btn-sm">Primary Button</button>
            <button class="btn btn-secondary btn-sm">Secondary Button</button>
            <button class="btn btn-accent btn-sm">Accent Button</button>
          </div>
        </div>
      </div>

      <!-- Status Colors -->
      <div class="card bg-base-100 shadow-lg">
        <div class="card-body">
          <h3 class="card-title text-secondary">Status Colors</h3>
          <div class="space-y-2">
            <div class="alert alert-info alert-sm">
              <Icon name="info" size="xs" />
              <span class="text-sm">Info alert</span>
            </div>
            <div class="alert alert-success alert-sm">
              <Icon name="success" size="xs" />
              <span class="text-sm">Success alert</span>
            </div>
            <div class="alert alert-warning alert-sm">
              <Icon name="warning" size="xs" />
              <span class="text-sm">Warning alert</span>
            </div>
            <div class="alert alert-error alert-sm">
              <Icon name="error" size="xs" />
              <span class="text-sm">Error alert</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Components -->
      <div class="card bg-base-100 shadow-lg">
        <div class="card-body">
          <h3 class="card-title text-accent">Components</h3>
          <div class="space-y-3">
            <div class="form-control">
              <input type="text" placeholder="Input field" class="input input-bordered input-sm" />
            </div>
            <div class="flex gap-2">
              <div class="badge badge-primary">Primary</div>
              <div class="badge badge-secondary">Secondary</div>
              <div class="badge badge-accent">Accent</div>
            </div>
            <progress class="progress progress-primary w-full" value="70" max="100"></progress>
          </div>
        </div>
      </div>
    </div>

    <!-- Sync Test Section -->
    <div class="card bg-base-100 shadow-lg">
      <div class="card-body">
        <h3 class="card-title text-warning">Sync Test</h3>
        <div class="alert alert-warning alert-sm mb-4">
          <Icon name="info" size="xs" />
          <div class="text-xs">
            <strong>Note:</strong> Items may show as "failed" because the backend server isn't running.
            This is normal for testing - the sync system is working correctly!
          </div>
        </div>
        <div class="space-y-3">
          <div class="alert alert-info">
            <Icon name="info" size="sm" />
            <div>
              <div>Total items: <strong>{{ totalCount }}</strong></div>
              <div>Pending: <strong>{{ pendingCount }}</strong> | Failed: <strong>{{ failedCount }}</strong></div>
              <div>Syncing: <strong>{{ syncingCount }}</strong> | Synced: <strong>{{ syncedCount }}</strong></div>
            </div>
          </div>
          <div class="flex gap-2 flex-wrap">
            <button @click="addTestSyncItem" class="btn btn-sm btn-secondary">
              <Icon name="plus" size="sm" class="mr-1" />
              Add Test Sync Item
            </button>
            <button @click="triggerSync" class="btn btn-sm btn-primary" :disabled="totalCount === 0">
              <Icon name="refresh" size="sm" class="mr-1" />
              Trigger Sync
            </button>
            <button @click="clearAllSync" class="btn btn-sm btn-error" :disabled="totalCount === 0">
              <Icon name="trash" size="sm" class="mr-1" />
              Clear All
            </button>
          </div>
          <div v-if="totalCount > 0" class="space-y-2">
            <h4 class="font-medium">Sync Items:</h4>
            <div v-for="item in pendingSubmissions" :key="item.id" class="flex items-center justify-between p-2 bg-base-200 rounded">
              <div class="flex items-center gap-2">
                <div class="w-2 h-2 rounded-full" :class="{
                  'bg-yellow-500': item.status === 'pending',
                  'bg-blue-500': item.status === 'syncing',
                  'bg-green-500': item.status === 'synced',
                  'bg-red-500': item.status === 'failed'
                }"></div>
                <span class="text-sm">{{ item.type }} - {{ item.status }}</span>
              </div>
              <button @click="removeItem(item.id)" class="btn btn-xs btn-ghost">✕</button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Reset to Light Theme -->
    <div class="text-center">
      <button @click="setTheme('light')" class="btn btn-primary">
        <Icon name="home" size="sm" class="mr-2" />
        Reset to Light Theme
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import Icon from './Icon.vue'
import { useOfflineStore } from '@/stores/offline'

const currentTheme = ref('light')
const offlineStore = useOfflineStore()

// Debug computed properties
const htmlTheme = computed(() => document.documentElement.getAttribute('data-theme') || 'none')
const primaryColor = computed(() => {
  const style = getComputedStyle(document.documentElement)
  return style.getPropertyValue('--p') || style.getPropertyValue('--primary') || 'not found'
})
const secondaryColor = computed(() => {
  const style = getComputedStyle(document.documentElement)
  return style.getPropertyValue('--s') || style.getPropertyValue('--secondary') || 'not found'
})

// Destructure offline store
const {
  isOnline,
  pendingCount,
  failedCount,
  pendingSubmissions,
  syncPendingSubmissions,
  clearAllSubmissions,
  removeSubmission,
  updateOnlineStatus,
  submitContactForm
} = offlineStore

// Additional computed properties for better debugging (with safe access)
const syncingCount = computed(() => pendingSubmissions?.filter((s: any) => s.status === 'syncing').length || 0)
const syncedCount = computed(() => pendingSubmissions?.filter((s: any) => s.status === 'synced').length || 0)
const totalCount = computed(() => pendingSubmissions?.length || 0)

const testThemes = [
  'light',
  'dark',
  'corporate',
  'business',
  'emerald',
  'forest',
  'luxury',
  'night',
  'winter',
  'cupcake',
  'bumblebee',
  'synthwave',
  'retro',
  'cyberpunk',
  'valentine',
  'halloween',
  'garden',
  'aqua',
  'lofi',
  'pastel',
  'fantasy',
  'wireframe',
  'black',
  'dracula',
  'cmyk',
  'autumn',
  'acid',
  'lemonade',
  'coffee'
]

const setTheme = (theme: string) => {
  currentTheme.value = theme
  document.documentElement.setAttribute('data-theme', theme)
  localStorage.setItem('theme', theme)
  console.log(`Theme changed to: ${theme}`)
}

// Sync test functions
const addTestSyncItem = async () => {
  const testData = {
    name: `Test User ${Date.now()}`,
    email: `test${Date.now()}@example.com`,
    message: `Test message created at ${new Date().toLocaleString()}`
  }

  console.log('=== ADDING TEST SYNC ITEM ===')
  console.log('Before - Total items:', totalCount.value)
  console.log('Before - Pending:', pendingCount)
  console.log('Before - Failed:', failedCount)

  // Temporarily simulate offline to force item to be queued
  const originalOnline = isOnline
  console.log('Original online status:', originalOnline)

  updateOnlineStatus(false)
  console.log('Set offline status to force queuing')

  // Add to offline store (this will queue it since we're "offline")
  const result = await submitContactForm(testData)
  console.log('Submit result:', result)
  console.log('Added test sync item (simulated offline):', testData)

  // Check counts after adding
  console.log('After adding - Total items:', totalCount.value)
  console.log('After adding - Pending:', pendingCount)

  // Restore online status after a short delay
  setTimeout(() => {
    updateOnlineStatus(originalOnline)
    console.log('Restored online status:', originalOnline)
    console.log('Final - Total items:', totalCount.value)
    console.log('Final - Pending:', pendingCount)
    console.log('Final - Failed:', failedCount)
    console.log('=== END ADD TEST ===')
  }, 500)
}

const triggerSync = async () => {
  console.log('Triggering manual sync...')
  await syncPendingSubmissions()
}

const clearAllSync = () => {
  console.log('Clearing all sync items...')
  clearAllSubmissions()
}

const removeItem = (id: string) => {
  console.log('Removing sync item:', id)
  removeSubmission(id)
}

// Debug function
const debugTheme = () => {
  const html = document.documentElement
  const computedStyle = getComputedStyle(html)

  console.log('=== THEME DEBUG ===')
  console.log('Current theme:', currentTheme.value)
  console.log('HTML data-theme:', html.getAttribute('data-theme'))
  console.log('Available CSS custom properties:')

  // Check for DaisyUI color variables
  const daisyUIVars = [
    '--p', '--pc', '--s', '--sc', '--a', '--ac',
    '--n', '--nc', '--b1', '--b2', '--b3', '--bc',
    '--in', '--inc', '--su', '--suc', '--wa', '--wac', '--er', '--erc'
  ]

  daisyUIVars.forEach(varName => {
    const value = computedStyle.getPropertyValue(varName)
    if (value) {
      console.log(`${varName}: ${value}`)
    }
  })

  // Check for standard CSS variables
  const standardVars = ['--primary', '--secondary', '--accent', '--base-100']
  standardVars.forEach(varName => {
    const value = computedStyle.getPropertyValue(varName)
    if (value) {
      console.log(`${varName}: ${value}`)
    }
  })

  console.log('=== END DEBUG ===')
}

onMounted(() => {
  // Load saved theme or default to light
  const savedTheme = localStorage.getItem('theme') || 'light'
  setTheme(savedTheme)

  // Initialize offline store
  offlineStore.initializeOfflineStore()
})
</script>

<style scoped>
.alert-sm {
  padding: 0.5rem;
  font-size: 0.875rem;
}

.btn:hover {
  transform: scale(1.05);
}

.card:hover {
  transform: translateY(-2px);
  transition: transform 0.2s ease;
}
</style>
