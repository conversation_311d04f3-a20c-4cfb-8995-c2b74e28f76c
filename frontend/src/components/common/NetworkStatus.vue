<template>
  <div v-if="!isOnline || hasPendingSubmissions" class="fixed top-16 left-4 right-4 z-40 md:left-auto md:right-4 md:w-80">
    <!-- Offline Status -->
    <div v-if="!isOnline" class="alert alert-warning shadow-lg mb-2">
      <div class="flex-1">
        <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current flex-shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
        </svg>
        <div>
          <h3 class="font-bold">You're offline</h3>
          <div class="text-xs">Your data will be saved and synced when you're back online</div>
        </div>
      </div>
    </div>

    <!-- Pending Submissions -->
    <div v-if="hasPendingSubmissions && !isDismissed" class="alert alert-info shadow-lg mb-2">
      <div class="flex-1">
        <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current flex-shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
        </svg>
        <div>
          <h3 class="font-bold">
            {{ totalItems }} item{{ totalItems !== 1 ? 's' : '' }} in sync queue
          </h3>
          <div class="text-xs">
            <span v-if="syncInProgress">Syncing...</span>
            <span v-else-if="pendingCount > 0 && isOnline">{{ pendingCount }} pending sync</span>
            <span v-else-if="failedCount > 0">{{ failedCount }} failed, {{ pendingCount }} pending</span>
            <span v-else-if="isOnline">Will sync automatically</span>
            <span v-else>Will sync when online</span>
          </div>
        </div>
      </div>
      <div class="flex-none">
        <button
          v-if="isOnline && !syncInProgress"
          @click="handleSync"
          class="btn btn-sm btn-primary"
        >
          Sync Now
        </button>
        <button
          v-if="totalItems > 0"
          @click="showPendingItems = !showPendingItems"
          class="btn btn-sm btn-ghost ml-2"
        >
          {{ showPendingItems ? 'Hide' : 'Show' }}
        </button>
        <button
          @click="dismissAlert"
          class="btn btn-sm btn-ghost ml-2 hover:btn-error"
          title="Dismiss alert (will reappear if new items are added)"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
    </div>

    <!-- Pending Items Details -->
    <div v-if="showPendingItems && pendingSubmissions.length > 0" class="alert alert-base-200 shadow-lg">
      <div class="w-full">
        <h4 class="font-semibold mb-2">Pending Items:</h4>
        <div class="space-y-2">
          <div 
            v-for="submission in pendingSubmissions" 
            :key="submission.id"
            class="flex items-center justify-between text-sm"
          >
            <div class="flex items-center space-x-2">
              <div 
                class="w-2 h-2 rounded-full"
                :class="{
                  'bg-yellow-500': submission.status === 'pending',
                  'bg-blue-500': submission.status === 'syncing',
                  'bg-green-500': submission.status === 'synced',
                  'bg-red-500': submission.status === 'failed'
                }"
              ></div>
              <span>{{ formatSubmissionType(submission.type) }}</span>
              <span class="text-xs opacity-70">{{ formatTimestamp(submission.timestamp) }}</span>
            </div>
            <div class="flex space-x-1">
              <button 
                v-if="submission.status === 'failed'" 
                @click="handleRetry(submission.id)"
                class="btn btn-xs btn-primary"
              >
                Retry
              </button>
              <button 
                @click="handleRemove(submission.id)"
                class="btn btn-xs btn-ghost"
              >
                ✕
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { usePWA } from '@/composables/usePWA'
import { useOfflineStore } from '@/stores/offline'

const { isOnline } = usePWA()
const offlineStore = useOfflineStore()

const showPendingItems = ref(false)
const isDismissed = ref(false)

const {
  hasPendingSubmissions,
  pendingCount,
  failedCount,
  pendingSubmissions,
  syncInProgress,
  syncPendingSubmissions,
  retrySubmission,
  removeSubmission,
} = offlineStore

// Computed property for total items (like in ThemeTest)
const totalItems = computed(() => pendingSubmissions?.length || 0)

const handleSync = async () => {
  await syncPendingSubmissions()
}

const handleRetry = async (submissionId: string) => {
  await retrySubmission(submissionId)
}

const handleRemove = (submissionId: string) => {
  removeSubmission(submissionId)
}

const dismissAlert = () => {
  isDismissed.value = true
  console.log('Sync alert dismissed')

  // Auto-show again if new items are added or after 30 seconds
  setTimeout(() => {
    if (totalItems.value > 0) {
      isDismissed.value = false
      console.log('Sync alert auto-restored due to pending items')
    }
  }, 30000) // Show again after 30 seconds if items still exist
}

// Watch for new items and auto-show alert
const lastItemCount = ref(totalItems.value)
const checkForNewItems = () => {
  if (totalItems.value > lastItemCount.value) {
    isDismissed.value = false // Show alert when new items are added
    console.log('Sync alert shown due to new items')
  }
  lastItemCount.value = totalItems.value
}

// Check for new items every few seconds
setInterval(checkForNewItems, 2000)

const formatSubmissionType = (type: string): string => {
  switch (type) {
    case 'contact':
      return 'Contact Form'
    case 'auth':
      return 'Authentication'
    default:
      return type.charAt(0).toUpperCase() + type.slice(1)
  }
}

const formatTimestamp = (timestamp: number): string => {
  const date = new Date(timestamp)
  const now = new Date()
  const diffMs = now.getTime() - date.getTime()
  const diffMins = Math.floor(diffMs / 60000)
  
  if (diffMins < 1) return 'Just now'
  if (diffMins < 60) return `${diffMins}m ago`
  if (diffMins < 1440) return `${Math.floor(diffMins / 60)}h ago`
  return date.toLocaleDateString()
}
</script>
