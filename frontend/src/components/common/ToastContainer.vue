<template>
  <div class="toast toast-top toast-end z-50">
    <TransitionGroup
      name="toast"
      tag="div"
      class="space-y-2"
    >
      <div
        v-for="toast in toasts"
        :key="toast.id"
        class="alert shadow-lg max-w-sm cursor-pointer"
        :class="getToastClass(toast.type)"
        @click="removeToast(toast.id)"
      >
        <Icon v-if="toast.icon" :name="toast.icon" size="sm" />
        <div class="flex-1 min-w-0">
          <div class="font-medium text-sm">{{ toast.title }}</div>
          <div v-if="toast.message" class="text-xs opacity-90 mt-1">{{ toast.message }}</div>
        </div>
        <button
          @click.stop="removeToast(toast.id)"
          class="btn btn-ghost btn-xs btn-circle ml-2"
        >
          <Icon name="x" size="xs" />
        </button>
      </div>
    </TransitionGroup>
  </div>
</template>

<script setup lang="ts">
import { useToast } from '@/composables/useToast'
import Icon from '@/components/common/Icon.vue'

const { toasts, removeToast } = useToast()

const getToastClass = (type: string) => {
  switch (type) {
    case 'success':
      return 'alert-success'
    case 'error':
      return 'alert-error'
    case 'warning':
      return 'alert-warning'
    case 'info':
      return 'alert-info'
    default:
      return 'alert-info'
  }
}
</script>

<style scoped>
/* Toast animations */
.toast-enter-active,
.toast-leave-active {
  transition: all 0.3s ease;
}

.toast-enter-from {
  opacity: 0;
  transform: translateX(100%);
}

.toast-leave-to {
  opacity: 0;
  transform: translateX(100%);
}

.toast-move {
  transition: transform 0.3s ease;
}

/* Hover effects */
.alert {
  transition: all 0.2s ease;
}

.alert:hover {
  transform: translateX(-4px);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}
</style>
