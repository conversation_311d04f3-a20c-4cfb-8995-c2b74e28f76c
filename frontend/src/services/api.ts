import axios, { type AxiosInstance, type AxiosResponse, type AxiosError } from 'axios'
import { useAuthStore } from '@/stores/auth'

// API Configuration
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001'
const API_VERSION = 'v1'

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: {
    message: string
    type?: string
    code?: string
  }
  message?: string
  timestamp?: string
}

export interface PaginatedResponse<T> {
  items: T[]
  pagination: {
    page: number
    limit: number
    total: number
    pages: number
  }
}

// API Error Class
export class ApiError extends Error {
  public status: number
  public code?: string
  public type?: string

  constructor(message: string, status: number, code?: string, type?: string) {
    super(message)
    this.name = 'ApiError'
    this.status = status
    this.code = code
    this.type = type
  }
}

// Create Axios Instance
class ApiService {
  private api: AxiosInstance

  constructor() {
    this.api = axios.create({
      baseURL: `${API_BASE_URL}/api/${API_VERSION}`,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    })

    this.setupInterceptors()
  }

  private setupInterceptors() {
    // Request Interceptor - Add auth token
    this.api.interceptors.request.use(
      (config) => {
        const authStore = useAuthStore()
        if (authStore.token) {
          config.headers.Authorization = `Bearer ${authStore.token}`
        }
        return config
      },
      (error) => Promise.reject(error)
    )

    // Response Interceptor - Handle errors globally
    this.api.interceptors.response.use(
      (response: AxiosResponse<ApiResponse>) => {
        return response
      },
      (error: AxiosError<ApiResponse>) => {
        const authStore = useAuthStore()

        // Handle 401 - Unauthorized
        if (error.response?.status === 401) {
          authStore.logout()
          window.location.href = '/login'
          return Promise.reject(new ApiError('Session expired', 401))
        }

        // Handle API errors
        if (error.response?.data) {
          const { error: apiError } = error.response.data
          throw new ApiError(
            apiError?.message || 'An error occurred',
            error.response.status,
            apiError?.code,
            apiError?.type
          )
        }

        // Handle network errors
        if (error.code === 'NETWORK_ERROR' || !error.response) {
          throw new ApiError('Network error. Please check your connection.', 0)
        }

        // Generic error
        throw new ApiError('An unexpected error occurred', error.response?.status || 500)
      }
    )
  }

  // Generic HTTP Methods
  async get<T>(url: string, params?: any): Promise<T> {
    const response = await this.api.get<ApiResponse<T>>(url, { params })
    return response.data.data as T
  }

  async post<T>(url: string, data?: any): Promise<T> {
    const response = await this.api.post<ApiResponse<T>>(url, data)
    return response.data.data as T
  }

  async put<T>(url: string, data?: any): Promise<T> {
    const response = await this.api.put<ApiResponse<T>>(url, data)
    return response.data.data as T
  }

  async delete<T>(url: string): Promise<T> {
    const response = await this.api.delete<ApiResponse<T>>(url)
    return response.data.data as T
  }

  // Get raw response (for cases where you need full response data)
  async getRaw<T>(url: string, params?: any): Promise<ApiResponse<T>> {
    const response = await this.api.get<ApiResponse<T>>(url, { params })
    return response.data
  }

  async postRaw<T>(url: string, data?: any): Promise<ApiResponse<T>> {
    const response = await this.api.post<ApiResponse<T>>(url, data)
    return response.data
  }

  async putRaw<T>(url: string, data?: any): Promise<ApiResponse<T>> {
    const response = await this.api.put<ApiResponse<T>>(url, data)
    return response.data
  }

  async deleteRaw<T>(url: string): Promise<ApiResponse<T>> {
    const response = await this.api.delete<ApiResponse<T>>(url)
    return response.data
  }
}

// Export singleton instance
export const apiService = new ApiService()
export default apiService
