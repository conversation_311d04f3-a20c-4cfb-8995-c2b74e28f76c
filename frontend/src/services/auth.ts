import { apiService } from './api'

// Auth Types
export interface User {
  id: number
  name: string
  email: string
  role: 'admin' | 'staff' | 'client'
  email_verified: boolean
  is_active: boolean
  profile?: any
  created_at: string
  updated_at: string
}

export interface LoginRequest {
  email: string
  password: string
}

export interface RegisterRequest {
  name: string
  email: string
  password: string
}

export interface LoginResponse {
  user: User
  token: string
  refreshToken: string
  expiresIn: number
  queued?: boolean
  emailId?: number
}

export interface RefreshTokenResponse {
  token: string
  expiresIn: number
}

export interface PasswordResetRequest {
  email: string
}

export interface PasswordResetConfirm {
  token: string
  password: string
}

export interface EmailVerificationRequest {
  token: string
}

// Authentication Service
class AuthService {
  // Login user
  async login(credentials: LoginRequest): Promise<LoginResponse> {
    return await apiService.post<LoginResponse>('/auth/login', credentials)
  }

  // Register new user
  async register(userData: RegisterRequest): Promise<LoginResponse> {
    return await apiService.post<LoginResponse>('/auth/register', userData)
  }

  // Refresh access token
  async refreshToken(refreshToken: string): Promise<RefreshTokenResponse> {
    return await apiService.post<RefreshTokenResponse>('/auth/refresh', {
      refreshToken,
    })
  }

  // Logout user
  async logout(): Promise<void> {
    try {
      await apiService.post('/auth/logout')
    } catch (error) {
      // Continue with logout even if API call fails
      console.warn('Logout API call failed:', error)
    }
  }

  // Get current user profile
  async getProfile(): Promise<User> {
    return await apiService.get<User>('/auth/profile')
  }

  // Update user profile
  async updateProfile(profileData: Partial<User>): Promise<User> {
    return await apiService.put<User>('/auth/profile', profileData)
  }

  // Request password reset
  async requestPasswordReset(data: PasswordResetRequest): Promise<{ message: string }> {
    return await apiService.post<{ message: string }>('/auth/forgot-password', data)
  }

  // Confirm password reset
  async confirmPasswordReset(data: PasswordResetConfirm): Promise<{ message: string }> {
    return await apiService.post<{ message: string }>('/auth/reset-password', data)
  }

  // Verify email address
  async verifyEmail(data: EmailVerificationRequest): Promise<{ message: string }> {
    return await apiService.post<{ message: string }>('/auth/verify-email', data)
  }

  // Resend email verification
  async resendEmailVerification(): Promise<{ message: string; emailId?: number }> {
    return await apiService.post<{ message: string; emailId?: number }>('/auth/resend-verification')
  }

  // Change password (when logged in)
  async changePassword(currentPassword: string, newPassword: string): Promise<{ message: string }> {
    return await apiService.post<{ message: string }>('/auth/change-password', {
      currentPassword,
      newPassword,
    })
  }

  // Check if email exists (for registration validation)
  async checkEmailExists(email: string): Promise<{ exists: boolean }> {
    return await apiService.get<{ exists: boolean }>(`/auth/check-email?email=${encodeURIComponent(email)}`)
  }

  // Validate token (check if user is still authenticated)
  async validateToken(): Promise<User> {
    return await apiService.get<User>('/auth/validate')
  }
}

// Export singleton instance
export const authService = new AuthService()
export default authService
