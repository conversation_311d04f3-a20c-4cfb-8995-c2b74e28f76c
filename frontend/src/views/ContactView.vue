<template>
  <div class="min-h-screen bg-base-100">
    <!-- Hero Section -->
    <section class="hero bg-gradient-to-r from-primary to-secondary text-primary-content py-20">
      <div class="hero-content text-center">
        <div class="max-w-md">
          <h1 class="mb-5 text-5xl font-bold">{{ $t('contact.title') }}</h1>
          <p class="mb-5 text-lg">Get in touch with our energy experts</p>
        </div>
      </div>
    </section>

    <!-- Contact Form Section -->
    <section class="section-padding">
      <div class="container-custom">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
          <!-- Contact Form -->
          <div class="card bg-base-100 shadow-xl">
            <div class="card-body">
              <h2 class="card-title text-2xl mb-6">{{ $t('contact.send') }}</h2>
              <form @submit.prevent="submitForm" class="space-y-4">
                <!-- Success Message -->
                <div v-if="submitMessage" class="alert alert-success">
                  <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span>{{ submitMessage }}</span>
                </div>

                <!-- Error Message -->
                <div v-if="submitError" class="alert alert-error">
                  <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span>{{ submitError }}</span>
                </div>

                <div class="form-control">
                  <label class="label">
                    <span class="label-text">{{ $t('contact.name') }}</span>
                  </label>
                  <input
                    type="text"
                    v-model="form.name"
                    class="input input-bordered w-full"
                    :placeholder="$t('contact.name')"
                    :disabled="isSubmitting"
                    required
                  />
                </div>

                <div class="form-control">
                  <label class="label">
                    <span class="label-text">{{ $t('contact.email') }}</span>
                  </label>
                  <input
                    type="email"
                    v-model="form.email"
                    class="input input-bordered w-full"
                    :placeholder="$t('contact.email')"
                    :disabled="isSubmitting"
                    required
                  />
                </div>

                <div class="form-control">
                  <label class="label">
                    <span class="label-text">{{ $t('contact.phone') }}</span>
                  </label>
                  <input
                    type="tel"
                    v-model="form.phone"
                    class="input input-bordered w-full"
                    :placeholder="$t('contact.phone')"
                    :disabled="isSubmitting"
                  />
                </div>

                <div class="form-control">
                  <label class="label">
                    <span class="label-text">{{ $t('contact.message') }}</span>
                  </label>
                  <textarea
                    v-model="form.message"
                    class="textarea textarea-bordered h-32"
                    :placeholder="$t('contact.message')"
                    :disabled="isSubmitting"
                    required
                  ></textarea>
                </div>

                <div class="form-control mt-6">
                  <button
                    type="submit"
                    class="btn btn-primary"
                    :disabled="isSubmitting"
                    :class="{ 'loading': isSubmitting }"
                  >
                    <span v-if="!isSubmitting">{{ $t('contact.send') }}</span>
                    <span v-else>Sending...</span>
                  </button>
                </div>
              </form>
            </div>
          </div>

          <!-- Contact Information -->
          <div class="space-y-6">
            <div class="card bg-base-100 shadow-xl">
              <div class="card-body">
                <h3 class="card-title">Contact Information</h3>
                <div class="space-y-4">
                  <div class="flex items-center space-x-3">
                    <svg class="w-5 h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                    </svg>
                    <span><EMAIL></span>
                  </div>
                  <div class="flex items-center space-x-3">
                    <svg class="w-5 h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                    </svg>
                    <span>+1 (555) 123-4567</span>
                  </div>
                  <div class="flex items-center space-x-3">
                    <svg class="w-5 h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                    <span>123 Energy Street, Green City, GC 12345</span>
                  </div>
                </div>
              </div>
            </div>

            <div class="card bg-base-100 shadow-xl">
              <div class="card-body">
                <h3 class="card-title">Business Hours</h3>
                <div class="space-y-2">
                  <div class="flex justify-between">
                    <span>Monday - Friday</span>
                    <span>9:00 AM - 6:00 PM</span>
                  </div>
                  <div class="flex justify-between">
                    <span>Saturday</span>
                    <span>10:00 AM - 4:00 PM</span>
                  </div>
                  <div class="flex justify-between">
                    <span>Sunday</span>
                    <span>Closed</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted } from 'vue'
import { useOfflineStore } from '@/stores/offline'
import { usePWA } from '@/composables/usePWA'
import { useFormAnalytics, useBusinessAnalytics } from '@/composables/useAnalytics'

const form = reactive({
  name: '',
  email: '',
  phone: '',
  message: ''
})

const isSubmitting = ref(false)
const submitMessage = ref('')
const submitError = ref('')

const offlineStore = useOfflineStore()
const { isOnline } = usePWA()

// Analytics tracking
const formAnalytics = useFormAnalytics('contact_form')
const businessAnalytics = useBusinessAnalytics()

const submitForm = async () => {
  if (isSubmitting.value) return

  try {
    isSubmitting.value = true
    submitError.value = ''
    submitMessage.value = ''

    const result = await offlineStore.submitContactForm({
      name: form.name,
      email: form.email,
      phone: form.phone || undefined,
      message: form.message,
      source: 'website'
    })

    if (result.offline) {
      submitMessage.value = isOnline.value
        ? 'Your message has been queued and will be sent shortly.'
        : 'Your message has been saved and will be sent when you\'re back online.'
    } else {
      submitMessage.value = 'Thank you for your message! We will get back to you soon.'
    }

    // Track successful form completion
    formAnalytics.trackFormComplete('contact_form')
    businessAnalytics.trackLeadGeneration('website', 'medium')
    businessAnalytics.trackServiceInquiry('general', 'contact_form')

    // Reset form
    Object.assign(form, {
      name: '',
      email: '',
      phone: '',
      message: ''
    })

    // Log success for demo purposes
    console.log('Contact form submitted successfully:', result)

  } catch (error: any) {
    submitError.value = error.message || 'Failed to submit form. Please try again.'
    console.error('Contact form submission error:', error)
  } finally {
    isSubmitting.value = false
  }
}

// Track form start when component mounts
onMounted(() => {
  formAnalytics.trackFormStart()
})
</script>
