<template>
  <div class="min-h-screen bg-gradient-to-br from-base-200/30 to-base-300/20">
    <!-- <PERSON> Header -->
    <div class="bg-gradient-to-r from-primary/10 via-secondary/10 to-accent/10 border-b border-base-300">
      <div class="max-w-6xl mx-auto px-6 py-12">
        <div class="text-center">
          <div class="flex justify-center mb-4">
            <div class="p-4 bg-primary/20 rounded-full">
              <Icon name="user" size="2xl" class="text-primary" />
            </div>
          </div>
          <h1 class="text-4xl md:text-5xl font-bold text-base-content mb-4">
            User Profile
          </h1>
          <p class="text-xl text-base-content/70 max-w-2xl mx-auto">
            Manage your account settings and preferences
          </p>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-6xl mx-auto px-6 py-12 space-y-12">

      <!-- Profile Content -->
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Profile Info Card -->
        <div class="lg:col-span-2">
          <div class="card bg-base-100 shadow-xl border border-base-200">
            <div class="card-body">
              <h2 class="card-title text-primary mb-6">
                <Icon name="user" size="md" />
                Profile Information
              </h2>
              
              <div class="form-control mb-4">
                <label class="label">
                  <span class="label-text">{{ t('auth.name') }}</span>
                </label>
                <input
                  type="text"
                  :value="user?.name || 'Admin User'"
                  class="input input-bordered"
                  readonly
                />
              </div>

              <div class="form-control mb-4">
                <label class="label">
                  <span class="label-text">{{ t('auth.email') }}</span>
                </label>
                <input
                  type="email"
                  :value="user?.email || '<EMAIL>'"
                  class="input input-bordered"
                  readonly
                />
              </div>

              <div class="form-control mb-4">
                <label class="label">
                  <span class="label-text">Role</span>
                </label>
                <input 
                  type="text" 
                  :value="user?.role || 'Administrator'" 
                  class="input input-bordered" 
                  readonly
                />
              </div>

              <div class="form-control mb-6">
                <label class="label">
                  <span class="label-text">Member Since</span>
                </label>
                <input
                  type="text"
                  :value="formatDate(user?.created_at || new Date())"
                  class="input input-bordered"
                  readonly
                />
              </div>

              <div class="card-actions">
                <button class="btn btn-primary" disabled>
                  <Icon name="edit" size="sm" class="mr-2" />
                  Edit Profile (Coming Soon)
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Quick Actions -->
        <div class="space-y-6">
          <!-- Account Actions -->
          <div class="card bg-base-100 shadow-xl border border-base-200">
            <div class="card-body">
              <h3 class="card-title text-secondary mb-4">
                <Icon name="bolt" size="md" />
                Quick Actions
              </h3>
              
              <div class="space-y-3">
                <RouterLink to="/dashboard" class="btn btn-outline btn-block">
                  <Icon name="chart-bar" size="sm" class="mr-2" />
                  Dashboard
                </RouterLink>
                
                <RouterLink to="/crm" class="btn btn-outline btn-block">
                  <Icon name="users" size="sm" class="mr-2" />
                  CRM System
                </RouterLink>
                
                <RouterLink v-if="isAdmin" to="/admin" class="btn btn-outline btn-block">
                  <Icon name="cog" size="sm" class="mr-2" />
                  Admin Panel
                </RouterLink>
                
                <button @click="handleLogout" class="btn btn-error btn-outline btn-block">
                  <Icon name="logout" size="sm" class="mr-2" />
                  {{ t('auth.logout') }}
                </button>
              </div>
            </div>
          </div>

          <!-- Account Stats -->
          <div class="card bg-base-100 shadow-xl border border-base-200">
            <div class="card-body">
              <h3 class="card-title text-accent mb-4">
                <Icon name="chart-bar" size="md" />
                Account Stats
              </h3>

              <div class="stats-enhanced stats-vertical">
                <div class="stat">
                  <div class="stat-title">Login Sessions</div>
                  <div class="stat-value text-primary">{{ loginCount }}</div>
                  <div class="stat-desc">This month</div>
                </div>

                <div class="stat">
                  <div class="stat-title">Last Login</div>
                  <div class="stat-value text-secondary text-sm">{{ lastLogin }}</div>
                  <div class="stat-desc">{{ formatDate(new Date()) }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- PWA Settings Section -->
      <section>
        <div class="text-center mb-8">
          <h2 class="text-3xl font-bold text-base-content mb-2">App Settings</h2>
          <p class="text-base-content/60">Manage your Progressive Web App experience</p>
        </div>

        <PWASettings />
      </section>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { useAuthStore } from '@/stores/auth'
import Icon from '@/components/common/Icon.vue'
import PWASettings from '@/components/PWASettings.vue'

const { t } = useI18n()

const router = useRouter()
const authStore = useAuthStore()

// Computed properties
const user = computed(() => authStore.user)
const isAdmin = computed(() => authStore.isAdmin)
const loginCount = computed(() => Math.floor(Math.random() * 50) + 10) // Mock data
const lastLogin = computed(() => 'Today')

// Methods
const formatDate = (date: string | Date) => {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  }).format(new Date(date))
}

const handleLogout = async () => {
  try {
    await authStore.logout()
    router.push('/login')
  } catch (error) {
    console.error('Logout error:', error)
  }
}
</script>

<style scoped>
.stats-vertical .stat {
  padding: 1rem;
}
</style>
