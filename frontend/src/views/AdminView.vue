<template>
  <div class="min-h-screen bg-gradient-to-br from-base-200/30 to-base-300/20">
    <!-- <PERSON> Header -->
    <div class="bg-gradient-to-r from-primary/10 via-secondary/10 to-accent/10 border-b border-base-300">
      <div class="max-w-7xl mx-auto px-6 py-12">
        <div class="text-center">
          <div class="flex justify-center mb-4">
            <div class="p-4 bg-primary/20 rounded-full">
              <Icon name="cog" size="2xl" class="text-primary" />
            </div>
          </div>
          <h1 class="text-4xl md:text-5xl font-bold text-base-content mb-4">
            Admin Dashboard
          </h1>
          <p class="text-xl text-base-content/70 max-w-2xl mx-auto">
            Complete system administration and management center
          </p>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto px-6 py-12 space-y-12">

      <!-- Enhanced Admin Dashboard -->
      <Suspense>
        <template #default>
          <AdminDashboard />
        </template>
        <template #fallback>
          <div class="flex items-center justify-center min-h-96">
            <div class="text-center">
              <div class="loading loading-spinner loading-lg text-primary"></div>
              <p class="mt-4 text-base-content/70">Loading dashboard...</p>
            </div>
          </div>
        </template>
      </Suspense>

      <!-- System Overview Stats -->
      <section>
        <div class="text-center mb-8">
          <h2 class="text-3xl font-bold text-base-content mb-2">System Overview</h2>
          <p class="text-base-content/60">Real-time system metrics and performance indicators</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-8">
          <!-- Total Users Card -->
          <div class="group">
            <div class="card bg-gradient-to-br from-primary/90 to-primary text-primary-content shadow-2xl border-0 transform transition-all duration-300 hover:scale-105 hover:shadow-3xl">
              <div class="card-body p-8">
                <div class="flex items-start justify-between mb-4">
                  <div class="p-3 bg-white/20 rounded-xl">
                    <Icon name="users" size="xl" class="text-white" />
                  </div>
                  <div class="text-right">
                    <div class="text-3xl font-bold mb-1">{{ totalUsers.toLocaleString() }}</div>
                    <div class="text-sm opacity-90">Total Users</div>
                  </div>
                </div>
                <div class="flex items-center justify-between">
                  <span class="text-sm opacity-80">{{ activeUsers }} active</span>
                  <div class="badge badge-success badge-sm">+12%</div>
                </div>
              </div>
            </div>
          </div>

          <!-- System Health Card -->
          <div class="group">
            <div class="card bg-gradient-to-br from-secondary/90 to-secondary text-secondary-content shadow-2xl border-0 transform transition-all duration-300 hover:scale-105 hover:shadow-3xl">
              <div class="card-body p-8">
                <div class="flex items-start justify-between mb-4">
                  <div class="p-3 bg-white/20 rounded-xl">
                    <Icon name="chart-bar" size="xl" class="text-white" />
                  </div>
                  <div class="text-right">
                    <div class="text-3xl font-bold mb-1">{{ systemHealth }}%</div>
                    <div class="text-sm opacity-90">System Health</div>
                  </div>
                </div>
                <div class="flex items-center justify-between">
                  <span class="text-sm opacity-80">All systems operational</span>
                  <div class="badge badge-success badge-sm">Excellent</div>
                </div>
              </div>
            </div>
          </div>

          <!-- API Requests Card -->
          <div class="group">
            <div class="card bg-gradient-to-br from-success/90 to-success text-success-content shadow-2xl border-0 transform transition-all duration-300 hover:scale-105 hover:shadow-3xl">
              <div class="card-body p-8">
                <div class="flex items-start justify-between mb-4">
                  <div class="p-3 bg-white/20 rounded-xl">
                    <Icon name="globe" size="xl" class="text-white" />
                  </div>
                  <div class="text-right">
                    <div class="text-3xl font-bold mb-1">{{ apiRequests.toLocaleString() }}</div>
                    <div class="text-sm opacity-90">API Requests</div>
                  </div>
                </div>
                <div class="flex items-center justify-between">
                  <span class="text-sm opacity-80">Last 24 hours</span>
                  <div class="badge badge-warning badge-sm">+8%</div>
                </div>
              </div>
            </div>
          </div>

          <!-- Storage Used Card -->
          <div class="group">
            <div class="card bg-gradient-to-br from-warning/90 to-warning text-warning-content shadow-2xl border-0 transform transition-all duration-300 hover:scale-105 hover:shadow-3xl">
              <div class="card-body p-8">
                <div class="flex items-start justify-between mb-4">
                  <div class="p-3 bg-white/20 rounded-xl">
                    <Icon name="database" size="xl" class="text-white" />
                  </div>
                  <div class="text-right">
                    <div class="text-3xl font-bold mb-1">{{ storageUsed }}%</div>
                    <div class="text-sm opacity-90">Storage Used</div>
                  </div>
                </div>
                <div class="flex items-center justify-between">
                  <span class="text-sm opacity-80">{{ storageGB }}GB used</span>
                  <div class="badge badge-info badge-sm">Normal</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Management Tools -->
      <section>
        <div class="text-center mb-8">
          <h2 class="text-3xl font-bold text-base-content mb-2">Management Tools</h2>
          <p class="text-base-content/60">Comprehensive system administration and control center</p>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <!-- System Management -->
          <div class="card bg-base-100/80 backdrop-blur-sm shadow-2xl border border-base-300/50 hover:shadow-3xl transition-all duration-300">
            <div class="card-body p-8">
              <div class="flex items-center mb-6">
                <div class="p-3 bg-primary/20 rounded-xl mr-4">
                  <Icon name="cog" size="lg" class="text-primary" />
                </div>
                <div>
                  <h3 class="text-2xl font-bold text-primary">System Management</h3>
                  <p class="text-base-content/60">Core system administration tools</p>
                </div>
              </div>

              <div class="grid grid-cols-1 gap-4">
                <button
                  @click="openUserManagement"
                  class="btn btn-lg bg-gradient-to-r from-primary/10 to-primary/5 border-primary/30 hover:from-primary hover:to-primary-focus hover:text-primary-content text-primary justify-start transition-all duration-300 transform hover:scale-[1.02]"
                >
                  <Icon name="users" size="md" class="mr-4" />
                  <div class="text-left">
                    <div class="font-semibold">User Management</div>
                    <div class="text-sm opacity-70">Manage users, roles, and permissions</div>
                  </div>
                </button>

                <button
                  @click="openSystemSettings"
                  class="btn btn-lg bg-gradient-to-r from-secondary/10 to-secondary/5 border-secondary/30 hover:from-secondary hover:to-secondary-focus hover:text-secondary-content text-secondary justify-start transition-all duration-300 transform hover:scale-[1.02]"
                >
                  <Icon name="cog" size="md" class="mr-4" />
                  <div class="text-left">
                    <div class="font-semibold">System Settings</div>
                    <div class="text-sm opacity-70">Configure application settings</div>
                  </div>
                </button>

                <button
                  @click="openDatabaseManagement"
                  class="btn btn-lg bg-gradient-to-r from-accent/10 to-accent/5 border-accent/30 hover:from-accent hover:to-accent-focus hover:text-accent-content text-accent justify-start transition-all duration-300 transform hover:scale-[1.02]"
                >
                  <Icon name="database" size="md" class="mr-4" />
                  <div class="text-left">
                    <div class="font-semibold">Database Management</div>
                    <div class="text-sm opacity-70">Database operations and maintenance</div>
                  </div>
                </button>

                <button
                  @click="openSecuritySettings"
                  class="btn btn-lg bg-gradient-to-r from-warning/10 to-warning/5 border-warning/30 hover:from-warning hover:to-warning-focus hover:text-warning-content text-warning justify-start transition-all duration-300 transform hover:scale-[1.02]"
                >
                  <Icon name="shield" size="md" class="mr-4" />
                  <div class="text-left">
                    <div class="font-semibold">Security Settings</div>
                    <div class="text-sm opacity-70">Security policies and monitoring</div>
                  </div>
                </button>

                <button
                  @click="openSystemLogs"
                  class="btn btn-lg bg-gradient-to-r from-info/10 to-info/5 border-info/30 hover:from-info hover:to-info-focus hover:text-info-content text-info justify-start transition-all duration-300 transform hover:scale-[1.02]"
                >
                  <Icon name="document" size="md" class="mr-4" />
                  <div class="text-left">
                    <div class="font-semibold">System Logs</div>
                    <div class="text-sm opacity-70">View and analyze system logs</div>
                  </div>
                </button>

                <button
                  @click="openMemoryMonitor"
                  class="btn btn-lg bg-gradient-to-r from-error/10 to-error/5 border-error/30 hover:from-error hover:to-error-focus hover:text-error-content text-error justify-start transition-all duration-300 transform hover:scale-[1.02]"
                >
                  <Icon name="activity" size="md" class="mr-4" />
                  <div class="text-left">
                    <div class="font-semibold">Memory & Performance Monitor</div>
                    <div class="text-sm opacity-70">Real-time performance analysis</div>
                  </div>
                </button>

                <button
                  @click="openSocketMonitor"
                  class="btn btn-lg bg-gradient-to-r from-info/10 to-info/5 border-info/30 hover:from-info hover:to-info-focus hover:text-info-content text-info justify-start transition-all duration-300 transform hover:scale-[1.02]"
                >
                  <Icon name="wifi" size="md" class="mr-4" />
                  <div class="text-left">
                    <div class="font-semibold">Socket.io Monitor</div>
                    <div class="text-sm opacity-70">Real-time Socket.io monitoring</div>
                  </div>
                </button>
              </div>
            </div>
          </div>

          <!-- Quick Actions -->
          <div class="card bg-base-100/80 backdrop-blur-sm shadow-2xl border border-base-300/50 hover:shadow-3xl transition-all duration-300">
            <div class="card-body p-8">
              <div class="flex items-center mb-6">
                <div class="p-3 bg-secondary/20 rounded-xl mr-4">
                  <Icon name="bolt" size="lg" class="text-secondary" />
                </div>
                <div>
                  <h3 class="text-2xl font-bold text-secondary">Quick Actions</h3>
                  <p class="text-base-content/60">Frequently used operations</p>
                </div>
              </div>

              <div class="grid grid-cols-1 gap-4">
                <RouterLink
                  to="/crm"
                  class="btn btn-lg bg-gradient-to-r from-primary/10 to-primary/5 border-primary/30 hover:from-primary hover:to-primary-focus hover:text-primary-content text-primary justify-start transition-all duration-300 transform hover:scale-[1.02]"
                >
                  <Icon name="chart-bar" size="md" class="mr-4" />
                  <div class="text-left">
                    <div class="font-semibold">CRM Dashboard</div>
                    <div class="text-sm opacity-70">Customer relationship management</div>
                  </div>
                </RouterLink>

                <RouterLink
                  to="/dashboard"
                  class="btn btn-lg bg-gradient-to-r from-secondary/10 to-secondary/5 border-secondary/30 hover:from-secondary hover:to-secondary-focus hover:text-secondary-content text-secondary justify-start transition-all duration-300 transform hover:scale-[1.02]"
                >
                  <Icon name="home" size="md" class="mr-4" />
                  <div class="text-left">
                    <div class="font-semibold">Main Dashboard</div>
                    <div class="text-sm opacity-70">Overview and analytics</div>
                  </div>
                </RouterLink>

                <button
                  @click="refreshSystem"
                  class="btn btn-lg bg-gradient-to-r from-accent/10 to-accent/5 border-accent/30 hover:from-accent hover:to-accent-focus hover:text-accent-content text-accent justify-start transition-all duration-300 transform hover:scale-[1.02]"
                >
                  <Icon name="refresh" size="md" class="mr-4" />
                  <div class="text-left">
                    <div class="font-semibold">Refresh System Data</div>
                    <div class="text-sm opacity-70">Update all system metrics</div>
                  </div>
                </button>

                <button
                  @click="exportData"
                  class="btn btn-lg bg-gradient-to-r from-info/10 to-info/5 border-info/30 hover:from-info hover:to-info-focus hover:text-info-content text-info justify-start transition-all duration-300 transform hover:scale-[1.02]"
                >
                  <Icon name="download" size="md" class="mr-4" />
                  <div class="text-left">
                    <div class="font-semibold">Export System Data</div>
                    <div class="text-sm opacity-70">Download system reports</div>
                  </div>
                </button>

                <button
                  @click="showMaintenanceMode"
                  class="btn btn-lg bg-gradient-to-r from-warning/10 to-warning/5 border-warning/30 hover:from-warning hover:to-warning-focus hover:text-warning-content text-warning justify-start transition-all duration-300 transform hover:scale-[1.02]"
                >
                  <Icon name="warning" size="md" class="mr-4" />
                  <div class="text-left">
                    <div class="font-semibold">Maintenance Mode</div>
                    <div class="text-sm opacity-70">System maintenance controls</div>
                  </div>
                </button>

                <button
                  @click="openSocketMonitor"
                  class="btn btn-lg bg-gradient-to-r from-info/10 to-info/5 border-info/30 hover:from-info hover:to-info-focus hover:text-info-content text-info justify-start transition-all duration-300 transform hover:scale-[1.02]"
                >
                  <Icon name="wifi" size="md" class="mr-4" />
                  <div class="text-left">
                    <div class="font-semibold">Socket.io Monitor</div>
                    <div class="text-sm opacity-70">Real-time connection monitoring</div>
                  </div>
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Recent Activity -->
      <section>
        <div class="text-center mb-8">
          <h2 class="text-3xl font-bold text-base-content mb-2">Recent Activity</h2>
          <p class="text-base-content/60">Latest system events and user activities</p>
        </div>

        <div class="card bg-base-100/80 backdrop-blur-sm shadow-2xl border border-base-300/50">
          <div class="card-body p-8">
            <div class="flex items-center justify-between mb-6">
              <div class="flex items-center">
                <div class="p-3 bg-accent/20 rounded-xl mr-4">
                  <Icon name="clock" size="lg" class="text-accent" />
                </div>
                <div>
                  <h3 class="text-2xl font-bold text-accent">System Activity Log</h3>
                  <p class="text-base-content/60">Real-time monitoring and events</p>
                </div>
              </div>
              <button @click="refreshSystem" class="btn btn-accent btn-sm">
                <Icon name="refresh" size="sm" class="mr-2" />
                Refresh
              </button>
            </div>

            <div class="overflow-x-auto">
              <table class="table table-zebra w-full">
                <thead>
                  <tr class="border-base-300">
                    <th class="bg-base-200/50 text-base-content font-semibold">Time</th>
                    <th class="bg-base-200/50 text-base-content font-semibold">Event</th>
                    <th class="bg-base-200/50 text-base-content font-semibold">User</th>
                    <th class="bg-base-200/50 text-base-content font-semibold">Status</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="activity in recentActivity" :key="activity.id" class="hover:bg-base-200/30 transition-colors">
                    <td class="font-mono text-sm">{{ activity.time }}</td>
                    <td class="font-medium">{{ activity.event }}</td>
                    <td class="text-base-content/80">{{ activity.user }}</td>
                    <td>
                      <div class="badge badge-lg" :class="getStatusBadgeClass(activity.status)">
                        {{ activity.status }}
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>

            <div class="flex justify-between items-center mt-6 pt-4 border-t border-base-300">
              <div class="text-sm text-base-content/60">
                Showing {{ recentActivity.length }} recent activities
              </div>
              <RouterLink to="/admin/logs" class="btn btn-outline btn-sm">
                View All Logs
                <Icon name="arrow-right" size="sm" class="ml-2" />
              </RouterLink>
            </div>
          </div>
        </div>
      </section>
    </div>
  </div>

  <!-- System Management Modals -->
  <UserManagement v-if="showUserManagement" @close="showUserManagement = false" />
  <SystemSettings v-if="showSystemSettings" @close="showSystemSettings = false" />
  <DatabaseManagement v-if="showDatabaseManagement" @close="showDatabaseManagement = false" />
  <SecuritySettings v-if="showSecuritySettings" @close="showSecuritySettings = false" />
  <SystemLogs v-if="showSystemLogs" @close="showSystemLogs = false" />

  <!-- Memory Monitor Modal -->
  <div v-if="showMemoryMonitor" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 backdrop-blur-sm">
    <div class="bg-base-100 rounded-xl shadow-2xl w-full max-w-6xl mx-4 max-h-[95vh] overflow-hidden">
      <div class="flex items-center justify-between p-6 border-b border-base-300">
        <h2 class="text-2xl font-bold">Memory & Performance Monitor</h2>
        <button @click="showMemoryMonitor = false" class="btn btn-ghost btn-sm">
          <Icon name="x" size="md" />
        </button>
      </div>
      <div class="p-6 overflow-y-auto max-h-[calc(95vh-80px)]">
        <MemoryMonitor />
      </div>
    </div>
  </div>

  <!-- Socket.io Monitor Modal -->
  <div v-if="showSocketMonitor" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 backdrop-blur-sm">
    <div class="bg-base-100 rounded-xl shadow-2xl w-full max-w-7xl mx-4 max-h-[95vh] overflow-hidden">
      <div class="flex items-center justify-between p-6 border-b border-base-300">
        <div class="flex items-center gap-3">
          <div class="p-2 bg-info/20 rounded-lg">
            <Icon name="wifi" size="lg" class="text-info" />
          </div>
          <div>
            <h2 class="text-2xl font-bold">Socket.io Real-time Monitor</h2>
            <p class="text-sm text-base-content/70">Live monitoring of Socket.io connections and events</p>
          </div>
        </div>
        <button @click="showSocketMonitor = false" class="btn btn-ghost btn-sm">
          <Icon name="x" size="md" />
        </button>
      </div>
      <div class="p-6 overflow-y-auto max-h-[calc(95vh-120px)]">
        <SocketMonitor />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, defineAsyncComponent } from 'vue'
import { useAuthStore } from '@/stores/auth'
import Icon from '@/components/common/Icon.vue'

// Lazy load heavy admin components for better performance
const UserManagement = defineAsyncComponent(() => import('@/components/admin/UserManagement.vue'))
const SystemSettings = defineAsyncComponent(() => import('@/components/admin/SystemSettings.vue'))
const DatabaseManagement = defineAsyncComponent(() => import('@/components/admin/DatabaseManagement.vue'))
const SecuritySettings = defineAsyncComponent(() => import('@/components/admin/SecuritySettings.vue'))
const SystemLogs = defineAsyncComponent(() => import('@/components/admin/SystemLogs.vue'))
const MemoryMonitor = defineAsyncComponent(() => import('@/components/admin/MemoryMonitor.vue'))
const SocketMonitor = defineAsyncComponent(() => import('@/components/admin/SocketMonitor.vue'))
const AdminDashboard = defineAsyncComponent(() => import('@/components/admin/AdminDashboard.vue'))

const authStore = useAuthStore()

// Modal visibility states
const showUserManagement = ref(false)
const showSystemSettings = ref(false)
const showDatabaseManagement = ref(false)
const showSecuritySettings = ref(false)
const showSystemLogs = ref(false)
const showMemoryMonitor = ref(false)
const showSocketMonitor = ref(false)

// Removed loading states - no longer needed without artificial delays

// Mock data for admin dashboard
const totalUsers = ref(1247)
const activeUsers = ref(892)
const systemHealth = ref(98)
const apiRequests = ref(15420)
const storageUsed = ref(67)
const storageGB = ref(134)

const recentActivity = ref([
  { id: 1, time: '10:30 AM', event: 'User login', user: '<EMAIL>', status: 'success' },
  { id: 2, time: '10:25 AM', event: 'Database backup', user: 'system', status: 'success' },
  { id: 3, time: '10:20 AM', event: 'API rate limit exceeded', user: 'api-user', status: 'warning' },
  { id: 4, time: '10:15 AM', event: 'New user registration', user: '<EMAIL>', status: 'success' },
  { id: 5, time: '10:10 AM', event: 'Failed login attempt', user: 'unknown', status: 'error' },
])

// Methods
const getStatusBadgeClass = (status: string) => {
  switch (status) {
    case 'success': return 'badge-success'
    case 'warning': return 'badge-warning'
    case 'error': return 'badge-error'
    default: return 'badge-info'
  }
}

const refreshSystem = () => {
  console.log('Refreshing system data...')
  // Mock refresh - in real app would fetch fresh data
  systemHealth.value = Math.floor(Math.random() * 10) + 90
  apiRequests.value = Math.floor(Math.random() * 5000) + 10000
}

const exportData = () => {
  console.log('Exporting system data...')
  alert('Export functionality would be implemented here')
}

const showMaintenanceMode = () => {
  console.log('Maintenance mode...')
  alert('Maintenance mode controls would be implemented here')
}

// Enhanced button handlers with loading states
const openUserManagement = () => {
  console.log('Opening User Management...')
  showUserManagement.value = true
}

const openSystemSettings = () => {
  console.log('Opening System Settings...')
  showSystemSettings.value = true
}

const openDatabaseManagement = () => {
  console.log('Opening Database Management...')
  showDatabaseManagement.value = true
}

const openSecuritySettings = () => {
  console.log('Opening Security Settings...')
  showSecuritySettings.value = true
}

const openSystemLogs = () => {
  console.log('Opening System Logs...')
  showSystemLogs.value = true
}

const openMemoryMonitor = () => {
  console.log('Opening Memory Monitor...')
  showMemoryMonitor.value = true
}

const openSocketMonitor = () => {
  console.log('Opening Socket.io Monitor...')
  showSocketMonitor.value = true
}
</script>

<style scoped>
.btn.justify-start {
  justify-content: flex-start;
}

/* Enhanced shadow effects */
.shadow-3xl {
  box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.25);
}

/* Smooth backdrop blur */
.backdrop-blur-sm {
  backdrop-filter: blur(4px);
}

/* Enhanced hover animations */
.group:hover .card {
  transform: translateY(-8px) scale(1.02);
}

/* Gradient text effects */
.text-gradient {
  background: linear-gradient(135deg, #02342b, #eaaa34);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Custom button hover effects */
.btn-enhanced {
  position: relative;
  overflow: hidden;
}

.btn-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-enhanced:hover::before {
  left: 100%;
}

/* Enhanced table styling */
.table th {
  position: sticky;
  top: 0;
  z-index: 10;
}

/* Smooth transitions for all interactive elements */
* {
  transition-property: transform, box-shadow, background-color, border-color, opacity;
  transition-duration: 200ms;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* Custom scrollbar for better aesthetics */
.overflow-x-auto::-webkit-scrollbar {
  height: 6px;
}

.overflow-x-auto::-webkit-scrollbar-track {
  background: theme('colors.base-200');
  border-radius: 3px;
}

.overflow-x-auto::-webkit-scrollbar-thumb {
  background: theme('colors.base-300');
  border-radius: 3px;
}

.overflow-x-auto::-webkit-scrollbar-thumb:hover {
  background: theme('colors.base-content/30');
}
</style>
