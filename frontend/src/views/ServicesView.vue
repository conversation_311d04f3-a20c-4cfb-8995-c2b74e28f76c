<template>
  <div class="min-h-screen bg-base-100">
    <!-- Hero Section -->
    <section class="hero bg-gradient-to-r from-primary to-secondary text-primary-content py-20">
      <div class="hero-content text-center">
        <div class="max-w-md">
          <h1 class="mb-5 text-5xl font-bold">{{ $t('services.title') }}</h1>
          <p class="mb-5 text-lg">Comprehensive energy solutions for your business</p>
        </div>
      </div>
    </section>

    <!-- Services Grid -->
    <section class="section-padding">
      <div class="container-custom">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <!-- Energy Audit -->
          <div class="card bg-base-100 shadow-xl">
            <div class="card-body">
              <div class="w-16 h-16 bg-primary rounded-full flex items-center justify-center mb-4">
                <svg class="w-8 h-8 text-primary-content" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
              </div>
              <h2 class="card-title">{{ $t('services.energy_audit') }}</h2>
              <p>{{ $t('services.energy_audit_desc') }}</p>
              <div class="card-actions justify-end">
                <button class="btn btn-primary">Learn More</button>
              </div>
            </div>
          </div>

          <!-- Energy Consultation -->
          <div class="card bg-base-100 shadow-xl">
            <div class="card-body">
              <div class="w-16 h-16 bg-secondary rounded-full flex items-center justify-center mb-4">
                <svg class="w-8 h-8 text-secondary-content" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </div>
              <h2 class="card-title">{{ $t('services.consultation') }}</h2>
              <p>{{ $t('services.consultation_desc') }}</p>
              <div class="card-actions justify-end">
                <button class="btn btn-secondary">Learn More</button>
              </div>
            </div>
          </div>

          <!-- Implementation Support -->
          <div class="card bg-base-100 shadow-xl">
            <div class="card-body">
              <div class="w-16 h-16 bg-accent rounded-full flex items-center justify-center mb-4">
                <svg class="w-8 h-8 text-accent-content" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
              </div>
              <h2 class="card-title">{{ $t('services.implementation') }}</h2>
              <p>{{ $t('services.implementation_desc') }}</p>
              <div class="card-actions justify-end">
                <button class="btn btn-accent">Learn More</button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
// Component logic can be added here
</script>
