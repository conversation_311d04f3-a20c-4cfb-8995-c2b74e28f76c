<template>
  <div class="performance-dashboard min-h-screen bg-base-200 p-4">
    <div class="container mx-auto max-w-7xl">
      <!-- Header -->
      <div class="mb-8">
        <h1 class="text-3xl font-bold text-base-content mb-2">
          Performance Dashboard
        </h1>
        <p class="text-base-content/70">
          Real-time performance metrics and business analytics
        </p>
      </div>

      <!-- Performance Score Card -->
      <div class="card bg-base-100 shadow-xl mb-8">
        <div class="card-body">
          <h2 class="card-title text-2xl mb-4">Overall Performance Score</h2>
          <div class="flex items-center justify-center">
            <div class="radial-progress text-primary text-6xl" :style="`--value:${performanceScore}`" role="progressbar">
              {{ performanceScore }}%
            </div>
          </div>
          <div class="text-center mt-4">
            <div class="badge badge-lg" :class="getScoreBadgeClass(performanceScore)">
              {{ getScoreLabel(performanceScore) }}
            </div>
          </div>
        </div>
      </div>

      <!-- Core Web Vitals -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div class="card bg-base-100 shadow-lg">
          <div class="card-body">
            <h3 class="card-title text-lg">Largest Contentful Paint</h3>
            <div class="text-3xl font-bold" :class="getMetricClass(metrics.lcp, 2500, 4000)">
              {{ formatTime(metrics.lcp) }}
            </div>
            <div class="text-sm text-base-content/70">Target: &lt; 2.5s</div>
          </div>
        </div>

        <div class="card bg-base-100 shadow-lg">
          <div class="card-body">
            <h3 class="card-title text-lg">First Input Delay</h3>
            <div class="text-3xl font-bold" :class="getMetricClass(metrics.fid, 100, 300)">
              {{ formatTime(metrics.fid) }}
            </div>
            <div class="text-sm text-base-content/70">Target: &lt; 100ms</div>
          </div>
        </div>

        <div class="card bg-base-100 shadow-lg">
          <div class="card-body">
            <h3 class="card-title text-lg">Cumulative Layout Shift</h3>
            <div class="text-3xl font-bold" :class="getMetricClass(metrics.cls, 0.1, 0.25, true)">
              {{ formatCLS(metrics.cls) }}
            </div>
            <div class="text-sm text-base-content/70">Target: &lt; 0.1</div>
          </div>
        </div>
      </div>

      <!-- Business Metrics -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <!-- Lead Generation -->
        <div class="card bg-base-100 shadow-xl">
          <div class="card-body">
            <h3 class="card-title text-xl mb-4">Lead Generation</h3>
            <div class="space-y-4">
              <div class="flex justify-between items-center">
                <span>Contact Form Submissions</span>
                <div class="badge badge-primary badge-lg">
                  {{ businessMetrics.leadGeneration.contactFormSubmissions }}
                </div>
              </div>
              <div class="flex justify-between items-center">
                <span>Email Signups</span>
                <div class="badge badge-secondary badge-lg">
                  {{ businessMetrics.leadGeneration.emailSignups }}
                </div>
              </div>
              <div class="flex justify-between items-center">
                <span>Phone Requests</span>
                <div class="badge badge-accent badge-lg">
                  {{ businessMetrics.leadGeneration.phoneCallRequests }}
                </div>
              </div>
              <div class="flex justify-between items-center">
                <span>Consultation Bookings</span>
                <div class="badge badge-success badge-lg">
                  {{ businessMetrics.leadGeneration.consultationBookings }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- User Engagement -->
        <div class="card bg-base-100 shadow-xl">
          <div class="card-body">
            <h3 class="card-title text-xl mb-4">User Engagement</h3>
            <div class="space-y-4">
              <div class="flex justify-between items-center">
                <span>Page Views</span>
                <div class="badge badge-info badge-lg">
                  {{ businessMetrics.userEngagement.pageViews }}
                </div>
              </div>
              <div class="flex justify-between items-center">
                <span>Avg. Session Duration</span>
                <div class="badge badge-warning badge-lg">
                  {{ formatDuration(businessMetrics.userEngagement.sessionDuration) }}
                </div>
              </div>
              <div class="flex justify-between items-center">
                <span>Bounce Rate</span>
                <div class="badge badge-error badge-lg">
                  {{ businessMetrics.userEngagement.bounceRate }}%
                </div>
              </div>
              <div class="flex justify-between items-center">
                <span>Return Visitors</span>
                <div class="badge badge-success badge-lg">
                  {{ businessMetrics.userEngagement.returnVisitors }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Resource Performance -->
      <div class="card bg-base-100 shadow-xl mb-8">
        <div class="card-body">
          <h3 class="card-title text-xl mb-4">Resource Performance</h3>
          <div class="overflow-x-auto">
            <table class="table table-zebra">
              <thead>
                <tr>
                  <th>Resource</th>
                  <th>Type</th>
                  <th>Load Time</th>
                  <th>Size</th>
                  <th>Status</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="resource in resourceTimings" :key="resource.name">
                  <td class="font-mono text-sm">{{ resource.name }}</td>
                  <td>
                    <div class="badge badge-outline">{{ resource.type }}</div>
                  </td>
                  <td>{{ formatTime(resource.duration) }}</td>
                  <td>{{ formatSize(resource.size) }}</td>
                  <td>
                    <div class="badge" :class="getResourceStatusClass(resource.duration)">
                      {{ getResourceStatus(resource.duration) }}
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <!-- Session Analytics -->
      <div class="card bg-base-100 shadow-xl">
        <div class="card-body">
          <h3 class="card-title text-xl mb-4">Current Session</h3>
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div class="stat">
              <div class="stat-title">Session ID</div>
              <div class="stat-value text-sm font-mono">{{ sessionAnalytics.sessionId?.slice(-8) }}</div>
            </div>
            <div class="stat">
              <div class="stat-title">Page Views</div>
              <div class="stat-value text-primary">{{ sessionAnalytics.pageViews }}</div>
            </div>
            <div class="stat">
              <div class="stat-title">Interactions</div>
              <div class="stat-value text-secondary">{{ sessionAnalytics.interactions }}</div>
            </div>
            <div class="stat">
              <div class="stat-title">Duration</div>
              <div class="stat-value text-accent">{{ formatDuration(sessionAnalytics.sessionDuration) }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { usePerformance } from '@/composables/usePerformance'
import { useAnalytics } from '@/composables/useAnalytics'

const { metrics, resourceTimings, getPerformanceScore } = usePerformance()
const { getBusinessMetrics, getSessionAnalytics } = useAnalytics()

const businessMetrics = ref(getBusinessMetrics())
const sessionAnalytics = ref(getSessionAnalytics())

const performanceScore = computed(() => getPerformanceScore() || 0)

// Format time in milliseconds
const formatTime = (ms: number | null): string => {
  if (ms === null) return 'N/A'
  if (ms < 1000) return `${Math.round(ms)}ms`
  return `${(ms / 1000).toFixed(2)}s`
}

// Format CLS score
const formatCLS = (cls: number | null): string => {
  if (cls === null) return 'N/A'
  return cls.toFixed(3)
}

// Format duration in seconds
const formatDuration = (ms: number): string => {
  if (ms < 60000) return `${Math.round(ms / 1000)}s`
  const minutes = Math.floor(ms / 60000)
  const seconds = Math.round((ms % 60000) / 1000)
  return `${minutes}m ${seconds}s`
}

// Format file size
const formatSize = (bytes: number): string => {
  if (bytes === 0) return 'N/A'
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(1024))
  return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`
}

// Get metric color class
const getMetricClass = (value: number | null, good: number, poor: number, reverse = false): string => {
  if (value === null) return 'text-base-content/50'
  
  if (reverse) {
    if (value <= good) return 'text-success'
    if (value <= poor) return 'text-warning'
    return 'text-error'
  } else {
    if (value <= good) return 'text-success'
    if (value <= poor) return 'text-warning'
    return 'text-error'
  }
}

// Get performance score badge class
const getScoreBadgeClass = (score: number): string => {
  if (score >= 90) return 'badge-success'
  if (score >= 70) return 'badge-warning'
  return 'badge-error'
}

// Get performance score label
const getScoreLabel = (score: number): string => {
  if (score >= 90) return 'Excellent'
  if (score >= 70) return 'Good'
  if (score >= 50) return 'Needs Improvement'
  return 'Poor'
}

// Get resource status
const getResourceStatus = (duration: number): string => {
  if (duration < 100) return 'Fast'
  if (duration < 500) return 'Good'
  if (duration < 1000) return 'Slow'
  return 'Very Slow'
}

// Get resource status class
const getResourceStatusClass = (duration: number): string => {
  if (duration < 100) return 'badge-success'
  if (duration < 500) return 'badge-info'
  if (duration < 1000) return 'badge-warning'
  return 'badge-error'
}

// Update metrics periodically
const updateMetrics = () => {
  businessMetrics.value = getBusinessMetrics()
  sessionAnalytics.value = getSessionAnalytics()
}

onMounted(() => {
  // Update metrics every 30 seconds
  const interval = setInterval(updateMetrics, 30000)
  
  // Cleanup on unmount
  return () => clearInterval(interval)
})
</script>

<style scoped>
.performance-dashboard {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.card {
  transition: all 0.3s ease;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.stat {
  text-align: center;
}

.stat-value {
  font-size: 1.5rem;
}

.radial-progress {
  --size: 8rem;
  --thickness: 8px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .performance-dashboard {
    padding: 1rem;
  }
  
  .radial-progress {
    --size: 6rem;
  }
  
  .stat-value {
    font-size: 1.2rem;
  }
}
</style>
